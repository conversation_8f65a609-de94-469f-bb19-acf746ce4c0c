#!/usr/bin/env python
# coding:utf-8

import pandas as pd
import numpy as np
import json
from collections import defaultdict

def simple_analyze_nasa_dataset():
    """
    简单分析NASA数据集的基本结构
    """
    print("开始分析NASA数据集...")

    # 手动读取CSV文件的前几行来了解结构
    with open('data/nasa_filtered.csv', 'r', encoding='utf-8') as f:
        lines = f.readlines()

    print(f'=== NASA数据集基本信息 ===')
    print(f'总行数: {len(lines)}')
    print(f'表头: {lines[0].strip()}')
    print()

    # 分析前10行数据
    print('=== 前5行数据样例 ===')
    for i in range(1, min(6, len(lines))):
        parts = lines[i].strip().split(',', 3)  # 最多分割3次，因为Text中可能包含逗号
        if len(parts) >= 4:
            title = parts[0]
            text = parts[1][:100] + "..." if len(parts[1]) > 100 else parts[1]
            cat2 = parts[2]
            cat3 = parts[3]
            print(f"样例 {i}:")
            print(f"  Title: {title}")
            print(f"  Text: {text}")
            print(f"  Cat2: {cat2}")
            print(f"  Cat3: {cat3}")
            print()

    # 统计Cat2和Cat3
    cat2_counts = {}
    cat3_counts = {}
    hierarchy = defaultdict(set)

    for i in range(1, len(lines)):
        try:
            # 更仔细地解析CSV行
            line = lines[i].strip()
            if not line:
                continue

            # 找到最后两个逗号的位置（Cat2和Cat3）
            parts = line.split(',')
            if len(parts) >= 4:
                cat2 = parts[-2].strip()
                cat3 = parts[-1].strip()

                if cat2:  # 忽略空的Cat2
                    cat2_counts[cat2] = cat2_counts.get(cat2, 0) + 1
                    hierarchy[cat2].add(cat3)

                if cat3:  # 忽略空的Cat3
                    cat3_counts[cat3] = cat3_counts.get(cat3, 0) + 1
        except Exception as e:
            print(f"解析第{i}行时出错: {e}")
            continue

    print('=== Cat2 (二级分类) 统计 ===')
    print(f'Cat2 唯一值数量: {len(cat2_counts)}')
    sorted_cat2 = sorted(cat2_counts.items(), key=lambda x: x[1], reverse=True)
    for cat2, count in sorted_cat2[:20]:  # 显示前20个
        print(f"  {cat2}: {count}")
    print()

    print('=== Cat3 (三级分类) 统计 ===')
    print(f'Cat3 唯一值数量: {len(cat3_counts)}')
    sorted_cat3 = sorted(cat3_counts.items(), key=lambda x: x[1], reverse=True)
    for cat3, count in sorted_cat3[:20]:  # 显示前20个
        print(f"  {cat3}: {count}")
    print()

    print('=== Cat2-Cat3 层次关系分析 ===')
    hierarchy_dict = {}
    for cat2, cat3_set in hierarchy.items():
        cat3_list = list(cat3_set)
        hierarchy_dict[cat2] = cat3_list
        print(f'{cat2}: {len(cat3_list)} 个子类别')
        if len(cat3_list) <= 5:
            print(f'  子类别: {cat3_list}')
        else:
            print(f'  子类别 (前5个): {cat3_list[:5]}...')
    print()

    # 保存层次结构到JSON文件
    try:
        with open('data/nasa_hierarchy.json', 'w', encoding='utf-8') as f:
            json.dump(hierarchy_dict, f, indent=2, ensure_ascii=False)
        print("层次结构已保存到 data/nasa_hierarchy.json")
    except Exception as e:
        print(f"保存层次结构时出错: {e}")

    return hierarchy_dict

if __name__ == '__main__':
    hierarchy = simple_analyze_nasa_dataset()
    print("分析完成！")
