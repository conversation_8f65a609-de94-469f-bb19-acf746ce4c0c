import requests

# 配置参数
api_key = "app-XQZO6aApSmTvpJNh6ALHdoxU"  # 替换为实际 API 密钥
base_url = "http://10.53.2.36:8080/v1"  # 替换为实际端点
headers = {
    "Authorization": f"Bearer {api_key}",
    "Content-Type": "application/json"
}

# 发送对话请求（blocking 模式）
data = {
    "query": "生成一个标题：Dify API 调用教程",
    "response_mode": "blocking",
    "user": "user-123"
}

response = requests.post(f"{base_url}/chat-messages", headers=headers, json=data)

if response.ok:
    result = response.json()
    print("生成结果：", result.get("answer"))
else:
    print("错误：", response.status_code, response.text)