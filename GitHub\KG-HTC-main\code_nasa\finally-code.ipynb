{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ed2af87d-229f-41d0-9966-d51be5f10d69", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\anaconda3\\envs\\chatglm\\lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import pandas as pd\n", "from qdrant_client import QdrantClient\n", "from sentence_transformers import SentenceTransformer\n", "from qdrant_client.http import models\n", "from tqdm import tqdm\n", "#import openai\n", "#import instructor\n", "#from pydantic import BaseModel, field_validator\n", "from typing import Literal\n", "from tenacity import (\n", "    retry,\n", "    stop_after_attempt,\n", "    wait_fixed,\n", ")\n", "from transformers import AutoTokenizer, AutoModelForCausalLM\n", "import torch\n", "import os\n", "\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import confusion_matrix, classification_report, roc_curve, auc, f1_score\n", "import seaborn as sns\n", "from sklearn.preprocessing import label_binarize\n", "from itertools import cycle\n", "\n", "from sklearn.metrics import accuracy_score\n", "\n", "# 设置环境变量\n", "os.environ['MASTER_ADDR'] = 'localhost'\n", "os.environ['MASTER_PORT'] = '29500'\n", "os.environ['WORLD_SIZE'] = '1'\n", "os.environ['RANK'] = '0'\n", "os.environ['LOCAL_RANK'] = '0'\n", "\n", "# 初始化分布式环境\n", "if torch.cuda.is_available():\n", "    torch.distributed.init_process_group(backend='nccl')\n", "else:\n", "    torch.distributed.init_process_group(backend='gloo')"]}, {"cell_type": "code", "execution_count": 2, "id": "c51f90f9-afae-46fa-a3b4-05781f051e62", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully read file using encoding: utf-8\n", "Successfully read file using encoding: utf-8\n", "Successfully read file using encoding: utf-8\n"]}], "source": ["\n", "# 尝试不同的编码格式\n", "encodings = ['utf-8', 'latin-1', 'cp1252', 'ISO-8859-1']\n", "\n", "for encoding in encodings:\n", "    try:\n", "        train_csv = pd.read_csv('wos/train_mini.csv', encoding=encoding)\n", "        print(f\"Successfully read file using encoding: {encoding}\")\n", "        break\n", "    except UnicodeDecodeError:\n", "        print(f\"Failed to read file using encoding: {encoding}\")\n", "else:\n", "    print(\"All encodings failed. Unable to read the file.\")\n", "\n", "\n", "for encoding in encodings:\n", "    try:\n", "        test_csv = pd.read_csv('wos/test_mini_2.csv', encoding=encoding)\n", "        print(f\"Successfully read file using encoding: {encoding}\")\n", "        break\n", "    except UnicodeDecodeError:\n", "        print(f\"Failed to read file using encoding: {encoding}\")\n", "else:\n", "    print(\"All encodings failed. Unable to read the file.\")\n", "\n", "#arxivData/train_80.csv\n", "for encoding in encodings:\n", "    try:\n", "        Label = pd.read_csv('Remote_balance/label.csv', encoding=encoding)\n", "        print(f\"Successfully read file using encoding: {encoding}\")\n", "        break\n", "    except UnicodeDecodeError:\n", "        print(f\"Failed to read file using encoding: {encoding}\")\n", "else:\n", "    print(\"All encodings failed. Unable to read the file.\")"]}, {"cell_type": "code", "execution_count": 3, "id": "373e3694-f8f9-484d-b6b2-0aab51818c13", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count</th>\n", "    </tr>\n", "    <tr>\n", "      <th>label_text</th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>光学数据产品</th>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>地形地貌数据</th>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>地面监测数据</th>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>基础地理数据</th>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>大气与海洋数据</th>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>社会经济数据</th>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>遥感反演数据产品</th>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>遥感应用数据产品</th>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>遥感样本数据</th>\n", "      <td>20</td>\n", "    </tr>\n", "    <tr>\n", "      <th>遥感解译数据产品</th>\n", "      <td>20</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            count\n", "label_text       \n", "光学数据产品         20\n", "地形地貌数据         20\n", "地面监测数据         20\n", "基础地理数据         20\n", "大气与海洋数据        20\n", "社会经济数据         20\n", "遥感反演数据产品       20\n", "遥感应用数据产品       20\n", "遥感样本数据         20\n", "遥感解译数据产品       20"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["pd.set_option('display.max_rows', 500)\n", "pd.DataFrame(train_csv.label_text.value_counts())"]}, {"cell_type": "code", "execution_count": 4, "id": "c1e0606f-c4dc-48a4-99ea-8f00cbe6d6d0", "metadata": {}, "outputs": [], "source": ["\n", "category_labels = train_csv.label_text.unique()"]}, {"cell_type": "code", "execution_count": 5, "id": "b448c17c-d38a-47e4-8927-4cbbfd21ae5b", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/miniconda3/envs/Rec/lib/python3.9/site-packages/torch/_utils.py:831: UserWarning: TypedStorage is deprecated. It will be removed in the future and UntypedStorage will be the only storage class. This should only matter to you if you are using storages directly.  To access UntypedStorage directly, use tensor.untyped_storage() instead of tensor.storage()\n", "  return self.fget.__get__(instance, owner)()\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "47f37ea2cd7149b896ac7b3dba741dd4", "version_major": 2, "version_minor": 0}, "text/plain": ["Batches:   0%|          | 0/7 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# create a point valid for qdrant\n", "qdrant_client = QdrantClient(\":memory:\")\n", "qdrant_client.create_collection('text-classification',vectors_config=models.VectorParams(size=1024, distance=models.Distance.COSINE))\n", "qdrant_label = QdrantClient(\":memory:\")\n", "qdrant_label.create_collection('label',vectors_config=models.VectorParams(size=1024, distance=models.Distance.COSINE))\n", "#embeddings = embedding_model.encode(train_csv['text'].tolist(), show_progress_bar=True)\n", "embedding_model = SentenceTransformer('/data/bge-m3')\n", "embeddings = embedding_model.encode(train_csv['text'].tolist(), show_progress_bar=True)"]}, {"cell_type": "code", "execution_count": 6, "id": "eb1ac85a", "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\nembeddings = []\\nfor text in train_csv[\\'text\\'].tolist():\\n    text=\"/\".join(chunck_text(text))\\n    embeddings.append(embedding_model.encode(text, show_progress_bar=False))\\n'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["'''\n", "embeddings = []\n", "for text in train_csv['text'].tolist():\n", "    text=\"/\".join(chunck_text(text))\n", "    embeddings.append(embedding_model.encode(text, show_progress_bar=False))\n", "'''"]}, {"cell_type": "code", "execution_count": 7, "id": "cdbd1dfa", "metadata": {}, "outputs": [], "source": ["embedding_label = embedding_model.encode(Label['label'].tolist(), show_progress_bar=False)"]}, {"cell_type": "code", "execution_count": 8, "id": "e8e36efc-90da-4cde-a80b-4aed91ec48ec", "metadata": {}, "outputs": [], "source": ["# Create and upload points to Qdrant\n", "points = []\n", "for idx, row in train_csv.iterrows():\n", "    point = models.PointStruct(\n", "        id=idx,  # Use the dataframe index as the point ID\n", "        vector=embeddings[idx].tolist(),  # Convert the embedding to a list\n", "        payload={'label_text': row['label_text'] , \"text\":row['text']}  # Use the label_text as the payload\n", "    )\n", "    points.append(point)\n", "qdrant_client.upload_points(collection_name='text-classification', points=points)"]}, {"cell_type": "code", "execution_count": 9, "id": "594c842b", "metadata": {}, "outputs": [], "source": ["# Create and upload points to Qdrant\n", "point_label = []\n", "for idx, row in Label.iterrows():\n", "    point = models.PointStruct(\n", "        id=idx,  # Use the dataframe index as the point ID\n", "        vector=embedding_label[idx].tolist(),  # Convert the embedding to a list\n", "        payload={'label': row['label'] , \"definition\":row['definition'],\"example\":row['example'],\"keyword\":row['keyword']}  # Use the label_text as the payload\n", "    )\n", "    point_label.append(point)\n", "qdrant_label.upload_points(collection_name='label', points=point_label)"]}, {"cell_type": "code", "execution_count": 10, "id": "5d65f000-0c6f-4f9d-a252-7096b5d81e59", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["原Query Text: 2022年云冈石窟9号窟地震灾害风险监测指标体系指标数据\n", "------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n"]}, {"data": {"text/plain": ["[ScoredPoint(id=101, version=0, score=0.5795994997024536, payload={'label_text': '社会经济数据', 'text': '2012-2022年东南亚11国灾害数据集（短期）'}, vector=None, shard_key=None, order_value=None),\n", " ScoredPoint(id=48, version=0, score=0.5774137377738953, payload={'label_text': '地面监测数据', 'text': '2021年山西省大同市云冈石窟第16窟有限元模型数据'}, vector=None, shard_key=None, order_value=None),\n", " ScoredPoint(id=155, version=0, score=0.5674664974212646, payload={'label_text': '遥感应用数据产品', 'text': '2013年8月东北松花江流域暴雨灾害风险评估数据集'}, vector=None, shard_key=None, order_value=None),\n", " ScoredPoint(id=37, version=0, score=0.5664111971855164, payload={'label_text': '地形地貌数据', 'text': '2019-2021四川GNSS滑坡变形监测数据'}, vector=None, shard_key=None, order_value=None),\n", " ScoredPoint(id=45, version=0, score=0.5520235300064087, payload={'label_text': '地面监测数据', 'text': '2012-2022年美国各州灾害数据集'}, vector=None, shard_key=None, order_value=None)]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["query_text = test_csv.iloc[4]['text']\n", "print(f\"原Query Text: {query_text}\")\n", "print(\"---\"*80)\n", "query_vector = embedding_model.encode(query_text)\n", "qdrant_client.search(collection_name='text-classification', query_vector=query_vector, limit=5,score_threshold=0.3)\n"]}, {"cell_type": "code", "execution_count": 11, "id": "5a92ae19-d958-4ad9-8f6f-9d7b8fa3d60f", "metadata": {}, "outputs": [], "source": ["def qdrant_search(query_text,top_k=5):\n", "    query_vector = embedding_model.encode(query_text)\n", "    search_response = qdrant_client.search(collection_name='text-classification', query_vector=query_vector, limit=top_k)\n", "    return search_response"]}, {"cell_type": "code", "execution_count": 12, "id": "c305f41f", "metadata": {}, "outputs": [], "source": ["def label_search(query_text,top_k=1):\n", "    query_vector = embedding_model.encode(query_text)\n", "    search_response = qdrant_label.search(collection_name='label', query_vector=query_vector, limit=top_k)\n", "    return search_response"]}, {"cell_type": "code", "execution_count": 13, "id": "26562ab0-0583-40e8-86ee-ec4ac505577f", "metadata": {}, "outputs": [], "source": ["categories_list = \"- \" + \"\\n- \".join(category_labels)\n", "system_prompt = f\"\"\"\n", "你是一个智能AI助理,需要进行一些数据进行分类\\n\n", "结合RAG给出的相似样本和他们的标签,对文本信息进行分类，分类标准严格参考下面所给标准，只能从其中选择一个：\n", "{categories_list}\n", "\"\"\"\n", "#结合RAG给出的5个相似样本和他们的标签,对文本信息进行分类，打一个0到1的分数，1表示完全匹配，0表示完全不匹配,分类标准严格参考下面所给标准，只能从其中选择一个：\n", "#{categories_list}"]}, {"cell_type": "code", "execution_count": 14, "id": "b2912c7e-aefe-4c3c-b7db-ce8ba0dbdab4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "你是一个智能AI助理,需要进行一些数据进行分类\n", "\n", "结合RAG给出的相似样本和他们的标签,对文本信息进行分类，分类标准严格参考下面所给标准，只能从其中选择一个：\n", "- 光学数据产品\n", "- 地形地貌数据\n", "- 地面监测数据\n", "- 基础地理数据\n", "- 大气与海洋数据\n", "- 社会经济数据\n", "- 遥感反演数据产品\n", "- 遥感应用数据产品\n", "- 遥感样本数据\n", "- 遥感解译数据产品\n", "\n"]}], "source": ["print(system_prompt)"]}, {"cell_type": "code", "execution_count": 15, "id": "be4ea292", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Building prefix dict from the default dictionary ...\n", "Loading model from cache /tmp/jieba.cache\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading model cost 0.636 seconds.\n", "Prefix dict has been built successfully.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Rank 1:\n", "Document: GOSAT（Greenhouse Gases Observing Satellite，温室气体观测卫星）二级数据样例（2012年4月、2013年1月）v2，为温室气体浓度监测提供数据支持。\n", "Label: 地面监测数据\n", "BM25 Score: 19.1383\n", "Vector Score: 0.4104\n", "Combined Score: 1.3673\n", "\n", "Rank 2:\n", "Document: 基于MODIS 和SABPM模型的全球月度海洋初级生产力数据集（自2003年起）\n", "Label: 大气与海洋数据\n", "BM25 Score: 13.9606\n", "Vector Score: 0.4096\n", "Combined Score: 1.1076\n", "\n", "Rank 3:\n", "Document: 标准化对地观测自适应立方体瓦片资源测试数据集-以北大西洋海域环境数据为例\n", "Label: 遥感反演数据产品\n", "BM25 Score: 0.3284\n", "Vector Score: 0.4775\n", "Combined Score: 0.4939\n", "\n"]}], "source": ["from rank_bm25 import BM25Okapi\n", "from qdrant_client import QdrantClient\n", "from qdrant_client.http.models import Filter\n", "import jieba\n", "\n", "\n", "# 从 Qdrant 获取文档数据并构建 BM25 索引\n", "def build_bm25_index():\n", "    # 从 Qdrant 滚动获取所有文档\n", "    documents = []\n", "    scroll_result = qdrant_client.scroll(\n", "        collection_name=\"text-classification\",\n", "        with_payload=True,  # 获取文档内容\n", "        limit=100  # 每次加载的文档数量，可调整\n", "    )\n", "    while scroll_result:\n", "        for point in scroll_result[0]:  # 获取点的列表\n", "            documents.append(point.payload[\"text\"])\n", "        if scroll_result[1] is None:  # 如果没有更多数据\n", "            break\n", "        scroll_result = qdrant_client.scroll(\n", "            collection_name=\"text-classification\",\n", "            with_payload=True,\n", "            limit=50,\n", "            offset=scroll_result[1]  # 获取下一个偏移量\n", "        )\n", "\n", "    # 构建 BM25 索引\n", "    tokenized_corpus = [list(jieba.cut(doc)) for doc in documents]\n", "    bm25 = BM25Okapi(tokenized_corpus)\n", "    return bm25, documents\n", "\n", "# 加载 BM25 索引\n", "bm25, documents = build_bm25_index()\n", "\n", "# 混合检索函数\n", "def hybrid_search(query_text, top_k=5):\n", "    # Step 1: 使用 BM25 检索\n", "    tokenized_query = list(jieba.cut(query_text))\n", "    bm25_scores = bm25.get_scores(tokenized_query)\n", "\n", "    # Step 2: 使用 Qdrant 向量搜索\n", "    query_vector = embedding_model.encode(query_text)  # 替换为你的向量化模型\n", "    vector_results = qdrant_client.search(\n", "        collection_name=\"text-classification\",\n", "        query_vector=query_vector,\n", "        limit=100\n", "    )\n", "\n", "    # Step 3: 综合 BM25 和向量分数\n", "    results = []\n", "    for res in vector_results:\n", "        doc_text = res.payload[\"text\"]  # 根据文档内容查找索引\n", "        label=res.payload[\"label_text\"]\n", "        bm25_idx = documents.index(doc_text)  # 动态获取 BM25 索引\n", "        bm25_score = bm25_scores[bm25_idx]\n", "        combined_score = bm25_score*0.05 + res.score\n", "        results.append({\n", "            \"text\": doc_text,\n", "            \"label\":label,\n", "            \"bm25_score\": bm25_score,\n", "            \"vector_score\": res.score,\n", "            \"combined_score\": combined_score\n", "        })\n", "\n", "    # 按综合分数排序\n", "    results = sorted(results, key=lambda x: x[\"combined_score\"], reverse=True)\n", "\n", "    return results[:top_k]\n", "\n", "# 测试检索\n", "query_text = \"Nonlinear System Identification: A User-Oriented Roadmap\"\n", "results = hybrid_search(query_text, top_k=3)\n", "\n", "# 输出结果\n", "for idx, result in enumerate(results):\n", "    print(f\"Rank {idx + 1}:\")\n", "    print(f\"Document: {result['text']}\")\n", "    print(f\"Label: {result['label']}\")\n", "    print(f\"BM25 Score: {result['bm25_score']:.4f}\")\n", "    print(f\"Vector Score: {result['vector_score']:.4f}\")\n", "    print(f\"Combined Score: {result['combined_score']:.4f}\\n\")\n"]}, {"cell_type": "code", "execution_count": 16, "id": "89da50a8", "metadata": {}, "outputs": [], "source": ["# 检索最近的100个，再通过加权计算出最前的两个标签\n", "def retrieve_and_weight_labels(query_text,top_k=5):\n", "    # 在 Qdrant 中搜索最近的100个匹配\n", "    search_results = hybrid_search(query_text,top_k)\n", "    \n", "    # 检查是否有结果\n", "    if not search_results:\n", "        return []\n", "    \n", "    # 创建一个字典来存储标签和它们的加权分数\n", "    label_scores = {}\n", "    \n", "    # 遍历搜索结果并计算加权分数\n", "    for result in search_results:\n", "        label = result['label']\n", "        score = result['combined_score']\n", "        \n", "        if label in label_scores:\n", "            label_scores[label] += score\n", "        else:\n", "            label_scores[label] = score\n", "    \n", "    # 将字典转换为列表并按分数排序\n", "    sorted_labels = sorted(label_scores.items(), key=lambda item: item[1], reverse=True)\n", "    \n", "    # 返回前两个标签\n", "    return [label for label, score in sorted_labels]"]}, {"cell_type": "code", "execution_count": 17, "id": "03758ade", "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "81a83685b42241ba8a758e457754127c", "version_major": 2, "version_minor": 0}, "text/plain": ["Loading checkpoint shards:   0%|          | 0/4 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["Qwen2ForCausalLM(\n", "  (model): Qwen2Model(\n", "    (embed_tokens): Embedding(152064, 3584)\n", "    (layers): ModuleList(\n", "      (0-27): 28 x Qwen2Decoder<PERSON>ayer(\n", "        (self_attn): Qwen2Attention(\n", "          (q_proj): Linear(in_features=3584, out_features=3584, bias=True)\n", "          (k_proj): Linear(in_features=3584, out_features=512, bias=True)\n", "          (v_proj): Linear(in_features=3584, out_features=512, bias=True)\n", "          (o_proj): Linear(in_features=3584, out_features=3584, bias=False)\n", "        )\n", "        (mlp): Qwen2MLP(\n", "          (gate_proj): Linear(in_features=3584, out_features=18944, bias=False)\n", "          (up_proj): Linear(in_features=3584, out_features=18944, bias=False)\n", "          (down_proj): Linear(in_features=18944, out_features=3584, bias=False)\n", "          (act_fn): SiLU()\n", "        )\n", "        (input_layernorm): Qwen2RMSNorm((3584,), eps=1e-06)\n", "        (post_attention_layernorm): Qwen2RMSNorm((3584,), eps=1e-06)\n", "      )\n", "    )\n", "    (norm): Qwen2RMSNorm((3584,), eps=1e-06)\n", "    (rotary_emb): Qwen2RotaryEmbedding()\n", "  )\n", "  (lm_head): Linear(in_features=3584, out_features=152064, bias=False)\n", ")"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["from transformers import AutoTokenizer, AutoModelForCausalLM\n", "#model_name = \"/dataSSD/zl/models/models/qwen2-7b-sft-lora-merged-remote\"  # 替换为你实际下载的开源模型名称\n", "model_name = \"/data/Qwen2.5-7B-Instruct\"  # 替换为你实际下载的开源模型名称\n", "tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)\n", "model = AutoModelForCausalLM.from_pretrained(model_name,\n", "    low_cpu_mem_usage=True,\n", "    trust_remote_code=True,\n", "    torch_dtype=torch.float16,\n", "    device_map=\"auto\")\n", "model.eval()"]}, {"cell_type": "code", "execution_count": 18, "id": "1baf7de3", "metadata": {}, "outputs": [], "source": ["\n", "def generate_cot_prompt(query_text,top_k=5):\n", "    labels= retrieve_and_weight_labels(query_text,top_k)\n", "    #labels=category_labels\n", "    prompt = f\"\"\"\n", "    你是一个智能AI助理,需要进行一些数据进行分类\\n\n", "    结合RAG给出的相似样本,对文本信息进行分类，分类标准严格参考下面所给标签和详细的标签说明，只能从其中选择一个\\n\\n\n", "    \"\"\"\n", "    # 初始化一个计数器来给所有标签统一编号\n", "    counter = 1\n", "\n", "    for label in labels:\n", "        label_embedding = embedding_model.encode(label)\n", "        search_response = qdrant_label.search(\n", "            collection_name='label',\n", "            query_vector=label_embedding,\n", "            limit=1\n", "        )\n", "        \n", "        sample_documents = [\n", "            {\n", "                \"标签\": result.payload['label'],\n", "                \"定义\": result.payload['definition'],\n", "                \"样例\": result.payload['example'],\n", "                \"关键字\": result.payload['keyword'],\n", "            } for result in search_response\n", "        ]\n", "        \n", "        for doc in sample_documents:\n", "            # 编号. 标签\n", "            prompt += f\"{counter}. {doc['标签']}\\n\"\n", "            # 定义：定义内容\n", "            prompt += f\"定义：{doc['定义']}\\n\"\n", "            # 示例：\n", "            prompt += \"样例:\\n\"\n", "            # 将 \"样例\" 字符串拆分成列表，并逐行添加\n", "            examples_list = doc['样例'].split('; ')\n", "            for example in examples_list:\n", "                prompt += f\"- {example}\\n\"\n", "            # 关键词：关键词字符串\n", "            prompt += f\"关键词：{doc['关键字']}\\n\\n\"\n", "            counter += 1\n", "\n", "    return prompt"]}, {"cell_type": "code", "execution_count": 19, "id": "dbe5bc02-56f2-471c-a38c-270b68fd2ba5", "metadata": {}, "outputs": [], "source": ["import re\n", "import torch\n", "from typing import List, Dict, Tuple\n", "       \n", "\n", "def classify_query_text(query_text, text_top, label_top) -> str:\n", "    # 准备检索到的样本文档\n", "    # 准备用户消息，将样本文档附加到用户消息中\n", "    # 使用集合来去重\n", "    #query_text=extract_word(query_text)\n", "    search_labels = qdrant_search(query_text, text_top)\n", "    \n", "\n", "    sample_documents = [\n", "        {\n", "            \"Text\": result.payload['text'],\n", "            \"Label\": result.payload['label_text'].strip(\"'\"),\n", "        } for result in search_labels\n", "    ]\n", "    '''\n", "    search_sample=hybrid_search(query_text, top_k=5)\n", "    sample_doc = [\n", "        {\n", "            \"Text\": result['text'],\n", "            \"Label\": result['label'],\n", "        } for result in search_sample\n", "    ]\n", "    '''\n", "    #system_prompt = generate_cot_prompt(query_text,label_top)\n", "    prompt = (\n", "        #f\"参考样本：{sample_documents}\\n\"\n", "        f\"文本内容：{query_text}\\n\"\n", "        \"请你根据文本内容和提供的标签，选择一个最合适的标签，只在所给标签中选择\"\n", "        \"请仅返回对应的标签，不做任何解释。\"\n", "    ) \n", "\n", "    messages = [\n", "         {\"role\": \"system\", \"content\": system_prompt},\n", "        {\"role\": \"user\", \"content\": prompt},\n", "      ]\n", "    text = tokenizer.apply_chat_template(\n", "      messages,\n", "      tokenize=False,\n", "      add_generation_prompt=True\n", "    )\n", "    model_inputs = tokenizer([text], return_tensors=\"pt\").to('cuda')\n", "\n", "    generated_ids = model.generate(\n", "        input_ids=model_inputs[\"input_ids\"],\n", "        attention_mask=model_inputs[\"attention_mask\"],\n", "        pad_token_id=tokenizer.eos_token_id,\n", "        max_new_tokens=512,\n", "        do_sample=True,\n", "        temperature=1.0,\n", "        top_p=0.9,\n", "        top_k=50,\n", "        repetition_penalty=1.0\n", "    )\n", "    generated_ids = [\n", "      output_ids[len(input_ids):] for input_ids, output_ids in zip(model_inputs[\"input_ids\"], generated_ids)\n", "    ]\n", "\n", "    response = tokenizer.batch_decode(generated_ids, skip_special_tokens=True)[0]\n", "    #match = re.search(r\"\\*\\*(.*?)\\*\\*\", response)\n", "    # 返回 CategoryModel 实例\n", "    return response"]}, {"cell_type": "code", "execution_count": 20, "id": "46e72211-37d1-4fc8-8e69-53c760fed5fc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "    你是一个智能AI助理,需要进行一些数据进行分类\n", "\n", "    结合RAG给出的相似样本,对文本信息进行分类，分类标准严格参考下面所给标签和详细的标签说明，只能从其中选择一个\n", "\n", "\n", "    1. 地面监测数据\n", "定义：通过地面观测站点或实地调查获取的监测数据\n", "样例:\n", "- 气象站点观测数据\n", "- 地面辐射测量数据\n", "- 土壤采样数据\n", "- 植被调查数据\n", "关键词：观测站; 实测数据; 调查数据\n", "\n", "2. 大气与海洋数据\n", "定义：描述大气和海洋环境要素的观测或模拟数据\n", "样例:\n", "- 气温气压数据\n", "- 风场数据产品\n", "- 海表温度数据\n", "- 海洋环流数据\n", "关键词：气象数据; 海洋数据; 环境要素\n", "\n", "3. 遥感反演数据产品\n", "定义：利用遥感数据通过物理或统计模型反演获得的参数产品\n", "样例:\n", "- 植被指数产品\n", "- 地表温度产品\n", "- 叶面积指数产品\n", "- 生物量估算产品\n", "关键词：反演; 参数; 模型; 估算\n", "\n", "4. 遥感应用数据产品\n", "定义：基于遥感数据通过专业分析和处理生成的应用型数据产品\n", "样例:\n", "- 土地利用/覆盖分类图\n", "- 农作物长势监测产品\n", "- 城市扩张变化图\n", "- 森林资源分布图\n", "关键词：专题图; 监测产品; 分类图; 变化分析\n", "\n", "5. 遥感样本数据\n", "定义：用于遥感分类、识别等任务的已标注训练数据\n", "样例:\n", "- 地物分类样本集\n", "- 变化检测训练数据\n", "- 地物识别验证样本\n", "- 典型地物光谱特征库\n", "关键词：样本; 训练数据; 验证数据; 标注数据\n", "\n", "\n"]}], "source": ["query_text=\"Nonlinear System Identification: A User-Oriented Roadmap\"\n", "#category = classify_query_text(query_text)\n", "prompt=generate_cot_prompt(query_text)\n", "print(prompt)"]}, {"cell_type": "code", "execution_count": 21, "id": "8a60a680", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 241/241 [00:58<00:00,  4.15it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["1 : text_top=1, label_top=5 -> 准确性: 0.3983, micro_f1: 0.3983, macro_f1: 0.3685\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 241/241 [00:56<00:00,  4.23it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2 : text_top=1, label_top=5 -> 准确性: 0.3859, micro_f1: 0.3859, macro_f1: 0.3597\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 241/241 [00:57<00:00,  4.16it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["3 : text_top=1, label_top=5 -> 准确性: 0.3983, micro_f1: 0.3983, macro_f1: 0.3730\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 241/241 [00:57<00:00,  4.21it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["4 : text_top=1, label_top=5 -> 准确性: 0.4066, micro_f1: 0.4066, macro_f1: 0.3873\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 241/241 [00:57<00:00,  4.18it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["5 : text_top=1, label_top=5 -> 准确性: 0.3983, micro_f1: 0.3983, macro_f1: 0.3780\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["\n", "from tqdm import tqdm\n", "from sklearn.metrics import accuracy_score, f1_score\n", "\n", "def run_evaluation(test_csv, text_top_list, label_top_list, num_iterations=5):\n", "    \"\"\"\n", "    循环评估分类模型性能，支持 text_top 和 label_top 的两层循环。\n", "\n", "    参数：\n", "        test_csv (DataFrame): 包含测试数据的 DataFrame，需要有 'text' 和 'label_text' 列。\n", "        text_top_list (list): text_top 的参数列表。\n", "        label_top_list (list): label_top 的参数列表。\n", "        num_iterations (int): 外层循环的次数。\n", "\n", "    返回：\n", "        None\n", "    \"\"\"\n", "\n", "\n", "    for text_top in text_top_list:\n", "        for label_top in label_top_list:\n", "            for i in range(num_iterations):\n", "                # 初始化预测列\n", "                test_csv['predicted_label'] = None\n", "\n", "                # 遍历测试数据集，分类并存储预测结果\n", "                for idx, row in tqdm(test_csv.iterrows(), total=test_csv.shape[0]):\n", "                    query_text = row['text']\n", "                    try:\n", "                        # 调用分类函数\n", "                        category = classify_query_text(query_text, text_top, label_top)\n", "                    except Exception as e:\n", "                        # 捕获分类函数的异常\n", "                        print(f\"Error classifying row {idx}: {e}\")\n", "                        category = \"Error\"\n", "\n", "                    test_csv.at[idx, 'predicted_label'] = category\n", "\n", "                # 从结果中提取真实标签和预测标签\n", "                labels_true = test_csv['label_text']\n", "                labels_pred = test_csv['predicted_label']\n", "\n", "                # 计算评估指标\n", "                try:\n", "                    accuracy = accuracy_score(labels_true, labels_pred)\n", "                    micro_f1 = f1_score(labels_true, labels_pred, average='micro')\n", "                    macro_f1 = f1_score(labels_true, labels_pred, average='macro')\n", "\n", "                    print(f\"{i + 1} : text_top={text_top}, label_top={label_top} -> 准确性: {accuracy:.4f}, micro_f1: {micro_f1:.4f}, macro_f1: {macro_f1:.4f}\")\n", "                except ValueError as e:\n", "                    # 捕获指标计算中的异常\n", "                    print(f\"Error calculating metrics: {e}\")\n", "\n", "# 示例调用\n", "text_top=[1]\n", "label_top=[5]\n", "run_evaluation(test_csv, text_top, label_top, num_iterations=5)\n"]}, {"cell_type": "code", "execution_count": 22, "id": "38ae22d2-565a-4ccd-8726-dde590baed8f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\ndata = {\\n    \"光学数据产品\": \"Optical\",\\n    \"遥感应用数据产品\": \"RS Application\",\\n    \"遥感反演数据产品\": \"RS Inversion\",\\n    \"社会经济数据\": \"Socioeconomic\",\\n    \"基础地理数据\": \"Geographic\",\\n    \"地面监测数据\": \"Ground\",\\n    \"遥感解译数据产品\": \"RS Interpretation\",\\n    \"大气与海洋数据\": \"Atmospheric\",\\n    \"遥感样本数据\": \"RS Sample\",\\n    \"地形地貌数据\": \"Topographic\"\\n}\\n\\n# 加载聚类结果数据集\\nclustering_results_df = submission\\n\\n# 假设聚类结果有一个\\'predicted_label\\'列，原始数据有一个\\'Category_Level_2_Numeric\\'列\\n# 直接从聚类结果数据集中获取这两列数据\\nlabels_true = clustering_results_df[\\'label_text\\']\\nlabels_pred = clustering_results_df[\\'predicted_label\\']\\n#labels_pred = clustering_results_df[\\'label_llm\\']\\n# 计算准确性\\naccuracy = accuracy_score(labels_true, labels_pred)\\nprint(f\\'准确性: {accuracy:.4f}\\')\\nmicro_f1 = f1_score(labels_true, labels_pred, average=\\'micro\\')\\nprint(f\\'micro_f1: {micro_f1:.4f}\\')\\nmacro_f1 = f1_score(labels_true, labels_pred, average=\\'macro\\')\\nprint(f\\'macro_f1: {macro_f1:.4f}\\')\\n\\n# 生成混淆矩阵\\nconf_matrix = confusion_matrix(labels_true, labels_pred)\\n# 将混淆矩阵的行列标签转换为英文\\n# 将混淆矩阵的行列标签转换为英文\\nconf_matrix_df = pd.DataFrame(conf_matrix, index=labels_true.unique(), columns=labels_pred.unique())\\nconf_matrix_df.index = conf_matrix_df.index.map(data)\\nconf_matrix_df.columns = conf_matrix_df.columns.map(data)\\n\\n# 绘制混淆矩阵\\nplt.figure(figsize=(10, 7))\\nsns.heatmap(conf_matrix_df, annot=True, fmt=\\'d\\', cmap=\\'Blues\\')\\nplt.xticks(rotation=30)\\nplt.yticks(rotation=30)\\nplt.show()\\n\\n# 生成分类报告\\nclass_report = classification_report(labels_true, labels_pred)\\nprint(class_report)\\n\\n# 添加AUC曲线评估\\n# 假设标签是多类别的，需要进行二值化处理\\nlabels_true_bin = label_binarize(labels_true, classes=labels_true.unique())\\nlabels_pred_bin = label_binarize(labels_pred, classes=labels_true.unique())\\n\\n# 计算每一类的ROC曲线和AUC值\\nfpr = dict()\\ntpr = dict()\\nroc_auc = dict()\\nn_classes = labels_true_bin.shape[1]\\n\\nfor i in range(n_classes):\\n    fpr[i], tpr[i], _ = roc_curve(labels_true_bin[:, i], labels_pred_bin[:, i])\\n    roc_auc[i] = auc(fpr[i], tpr[i])\\n\\n# 绘制所有类别的ROC曲线\\nplt.figure(figsize=(10, 7))\\ncolors = cycle([\\'blue\\', \\'red\\', \\'green\\', \\'purple\\', \\'orange\\', \\'brown\\'])\\nfor i, color in zip(range(n_classes), colors):\\n    plt.plot(fpr[i], tpr[i], color=color, lw=2,\\n             label=f\\'ROC curve of class {i} (area = {roc_auc[i]:.2f})\\')\\n\\nplt.plot([0, 1], [0, 1], \\'k--\\', lw=2)\\nplt.xlim([0.0, 1.0])\\nplt.ylim([0.0, 1.05])\\nplt.xlabel(\\'False Positive Rate\\')\\nplt.ylabel(\\'True Positive Rate\\')\\nplt.show()\\n\\n'"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "'''\n", "data = {\n", "    \"光学数据产品\": \"Optical\",\n", "    \"遥感应用数据产品\": \"RS Application\",\n", "    \"遥感反演数据产品\": \"RS Inversion\",\n", "    \"社会经济数据\": \"Socioeconomic\",\n", "    \"基础地理数据\": \"Geographic\",\n", "    \"地面监测数据\": \"Ground\",\n", "    \"遥感解译数据产品\": \"RS Interpretation\",\n", "    \"大气与海洋数据\": \"Atmospheric\",\n", "    \"遥感样本数据\": \"RS Sample\",\n", "    \"地形地貌数据\": \"Topographic\"\n", "}\n", "\n", "# 加载聚类结果数据集\n", "clustering_results_df = submission\n", "\n", "# 假设聚类结果有一个'predicted_label'列，原始数据有一个'Category_Level_2_Numeric'列\n", "# 直接从聚类结果数据集中获取这两列数据\n", "labels_true = clustering_results_df['label_text']\n", "labels_pred = clustering_results_df['predicted_label']\n", "#labels_pred = clustering_results_df['label_llm']\n", "# 计算准确性\n", "accuracy = accuracy_score(labels_true, labels_pred)\n", "print(f'准确性: {accuracy:.4f}')\n", "micro_f1 = f1_score(labels_true, labels_pred, average='micro')\n", "print(f'micro_f1: {micro_f1:.4f}')\n", "macro_f1 = f1_score(labels_true, labels_pred, average='macro')\n", "print(f'macro_f1: {macro_f1:.4f}')\n", "\n", "# 生成混淆矩阵\n", "conf_matrix = confusion_matrix(labels_true, labels_pred)\n", "# 将混淆矩阵的行列标签转换为英文\n", "# 将混淆矩阵的行列标签转换为英文\n", "conf_matrix_df = pd.DataFrame(conf_matrix, index=labels_true.unique(), columns=labels_pred.unique())\n", "conf_matrix_df.index = conf_matrix_df.index.map(data)\n", "conf_matrix_df.columns = conf_matrix_df.columns.map(data)\n", "\n", "# 绘制混淆矩阵\n", "plt.figure(figsize=(10, 7))\n", "sns.heatmap(conf_matrix_df, annot=True, fmt='d', cmap='Blues')\n", "plt.xticks(rotation=30)\n", "plt.yticks(rotation=30)\n", "plt.show()\n", "\n", "# 生成分类报告\n", "class_report = classification_report(labels_true, labels_pred)\n", "print(class_report)\n", "\n", "# 添加AUC曲线评估\n", "# 假设标签是多类别的，需要进行二值化处理\n", "labels_true_bin = label_binarize(labels_true, classes=labels_true.unique())\n", "labels_pred_bin = label_binarize(labels_pred, classes=labels_true.unique())\n", "\n", "# 计算每一类的ROC曲线和AUC值\n", "fpr = dict()\n", "tpr = dict()\n", "roc_auc = dict()\n", "n_classes = labels_true_bin.shape[1]\n", "\n", "for i in range(n_classes):\n", "    fpr[i], tpr[i], _ = roc_curve(labels_true_bin[:, i], labels_pred_bin[:, i])\n", "    roc_auc[i] = auc(fpr[i], tpr[i])\n", "\n", "# 绘制所有类别的ROC曲线\n", "plt.figure(figsize=(10, 7))\n", "colors = cycle(['blue', 'red', 'green', 'purple', 'orange', 'brown'])\n", "for i, color in zip(range(n_classes), colors):\n", "    plt.plot(fpr[i], tpr[i], color=color, lw=2,\n", "             label=f'ROC curve of class {i} (area = {roc_auc[i]:.2f})')\n", "\n", "plt.plot([0, 1], [0, 1], 'k--', lw=2)\n", "plt.xlim([0.0, 1.0])\n", "plt.ylim([0.0, 1.05])\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "plt.show()\n", "\n", "'''"]}, {"cell_type": "code", "execution_count": null, "id": "8d010443-ae02-4a92-9617-d937205bd3f3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "chatglm", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}