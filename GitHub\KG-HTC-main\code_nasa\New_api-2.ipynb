{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ed2af87d-229f-41d0-9966-d51be5f10d69", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from qdrant_client import QdrantClient\n", "#from sentence_transformers import SentenceTransformer\n", "from qdrant_client.http import models\n", "from tqdm import tqdm\n", "\n", "import os\n", "\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import confusion_matrix, classification_report, roc_curve, auc, f1_score\n", "import seaborn as sns\n", "from sklearn.preprocessing import label_binarize\n", "from itertools import cycle\n", "\n", "from sklearn.metrics import accuracy_score\n", "from openai import OpenAI\n"]}, {"cell_type": "code", "execution_count": 2, "id": "07fc2cc9", "metadata": {}, "outputs": [], "source": ["import httpx\n", "import logging\n", "import warnings\n", "from urllib3.exceptions import InsecureRequestWarning\n", "# 禁用 httpx 的详细日志\n", "logging.getLogger(\"httpx\").setLevel(logging.WARNING)\n", "\n", "# 禁用 SSL 警告\n", "warnings.filterwarnings('ignore', category=InsecureRequestWarning)\n", "\n", "\n", "# 精确控制各模块的日志级别\n", "logging.getLogger('urllib3').setLevel(logging.ERROR)\n", "logging.getLogger('backoff').setLevel(logging.ERROR)\n", "logging.getLogger('posthog').setLevel(logging.ERROR)\n", "# 完全禁用 backoff 的日志\n", "logging.getLogger('backoff').disabled = True\n"]}, {"cell_type": "code", "execution_count": 3, "id": "c51f90f9-afae-46fa-a3b4-05781f051e62", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully read file using encoding: utf-8\n", "Successfully read file using encoding: utf-8\n"]}], "source": ["\n", "# 尝试不同的编码格式\n", "encodings = ['utf-8', 'latin-1', 'cp1252', 'ISO-8859-1']\n", "\n", "for encoding in encodings:\n", "    try:\n", "        train_csv = pd.read_csv('dataset/nasa/nasa_train.csv', encoding=encoding)\n", "        print(f\"Successfully read file using encoding: {encoding}\")\n", "        break\n", "    except UnicodeDecodeError:\n", "        print(f\"Failed to read file using encoding: {encoding}\")\n", "else:\n", "    print(\"All encodings failed. Unable to read the file.\")\n", "\n", "\n", "for encoding in encodings:\n", "    try:\n", "        test_csv = pd.read_csv('dataset/nasa/nasa_val.csv', encoding=encoding)\n", "        print(f\"Successfully read file using encoding: {encoding}\")\n", "        break\n", "    except UnicodeDecodeError:\n", "        print(f\"Failed to read file using encoding: {encoding}\")\n", "else:\n", "    print(\"All encodings failed. Unable to read the file.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "7cbc0b3c", "metadata": {}, "outputs": [], "source": ["pd.set_option('display.max_rows', 500)\n", "pd.DataFrame(train_csv.Cat3.value_counts())"]}, {"cell_type": "code", "execution_count": null, "id": "1c09e4dd", "metadata": {}, "outputs": [], "source": ["pd.set_option('display.max_rows', 500)\n", "pd.DataFrame(test_csv.Cat3.value_counts())"]}, {"cell_type": "code", "execution_count": 4, "id": "6a7f2306", "metadata": {}, "outputs": [], "source": ["openai_client = OpenAI(api_key=\"sk-ayjtsvatlmfjzjhkecmomyvgrwbjqbxceldglnouflxzvbvx\",base_url=\"https://api.siliconflow.cn/v1\")"]}, {"cell_type": "code", "execution_count": 5, "id": "62434a5d-28f1-4884-9ec7-4220b1578f25", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# create a point valid for qdrant\n", "qdrant_client = QdrantClient(\":memory:\")\n", "qdrant_client.create_collection('text-classification',vectors_config=models.VectorParams(size=1024, distance=models.Distance.COSINE))\n", "qdrant_label = QdrantClient(\":memory:\")\n", "qdrant_label.create_collection('label',vectors_config=models.VectorParams(size=1024, distance=models.Distance.COSINE))\n"]}, {"cell_type": "code", "execution_count": 6, "id": "63b628a3", "metadata": {}, "outputs": [], "source": ["def get_embeddings(texts):\n", "    try:\n", "        # 调用嵌入模型\n", "        response = openai_client.embeddings.create(\n", "            input=texts,\n", "            model=\"BAAI/bge-m3\"\n", "        )\n", "\n", "        # 从响应中提取嵌入向量\n", "        embedding = response.data[0].embedding  # 单文本只有一个嵌入向量\n", "        return embedding\n", "    except Exception as e:\n", "        print(f\"发生错误: {e}\")\n", "        return None\n", "\n", "# 示例文本数据\n"]}, {"cell_type": "code", "execution_count": 7, "id": "e076be08", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功获取 3404 个文本的嵌入向量。\n", "第一个文本的嵌入向量长度: 1024\n"]}], "source": ["import time\n", "def get_openai_embeddings(texts):\n", "    \"\"\"\n", "    使用 OpenAI API 对输入的文本列表进行向量化\n", "    :param texts: 待向量化的文本列表\n", "    :return: 文本对应的嵌入向量列表\n", "    \"\"\"\n", "    batch_size = 64\n", "    all_embeddings = []\n", "    start_time = time.time()\n", "    try:\n", "        for i in range(0, len(texts), batch_size):\n", "            current_time = time.time()\n", "            elapsed_time = current_time - start_time\n", "            if elapsed_time >= 20:\n", "                print(\"已连续运行 30 秒，暂停 10 秒...\")\n", "                time.sleep(10)\n", "                start_time = time.time()\n", "\n", "            batch_texts = texts[i:i + batch_size]\n", "            response = openai_client.embeddings.create(\n", "                input=batch_texts,  # 修改这里：使用当前批次的文本\n", "                model=\"BAAI/bge-m3\"  # 建议使用OpenAI官方模型\n", "            )\n", "            embeddings = [data.embedding for data in response.data]\n", "            all_embeddings.extend(embeddings)\n", "        return all_embeddings\n", "    except Exception as e:\n", "        print(f\"调用 OpenAI API 时发生错误: {e}\")\n", "        return None\n", "\n", "# 使用 OpenAI API 获取嵌入向量\n", "embeddings = get_openai_embeddings(train_csv['Title'].tolist())\n", "\n", "\n", "if embeddings:\n", "    print(f\"成功获取 {len(embeddings)} 个文本的嵌入向量。\")\n", "    # 示例：打印第一个文本的嵌入向量长度\n", "    print(f\"第一个文本的嵌入向量长度: {len(embeddings[0])}\")\n", "else:\n", "    print(\"未能成功获取嵌入向量。\")\n", "    "]}, {"cell_type": "code", "execution_count": 8, "id": "c1e0606f-c4dc-48a4-99ea-8f00cbe6d6d0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['ocean temperature' 'ocean optics' 'gravity/gravitational field'\n", " 'microwave' 'marine sediments' 'land surface/agriculture indicators'\n", " 'snow/ice' 'ecosystems' 'radar' 'land use/land cover'\n", " 'atmospheric radiation' 'atmospheric water vapor' 'ocean circulation'\n", " 'salinity/density' 'clouds' 'topography' 'surface water' 'boundaries'\n", " 'animals/vertebrates' 'aerosols' 'surface radiative properties'\n", " 'atmospheric chemistry' 'ocean winds' 'geomagnetism' 'soils'\n", " 'air quality' 'bathymetry/seafloor topography' 'ocean chemistry'\n", " 'platform characteristics' '-na-' 'vegetation' 'environmental impacts'\n", " 'precipitation' 'plants' 'atmospheric electricity'\n", " 'atmospheric/ocean indicators' 'marine geophysics' 'sea ice'\n", " 'rocks/minerals/crystals' 'fungi' 'atmospheric temperature'\n", " 'visible wavelengths' 'glaciers/ice sheets' 'infrastructure'\n", " 'ecological dynamics' 'atmospheric winds' 'altitude'\n", " 'infrared wavelengths' 'population' 'animals/invertebrates'\n", " 'surface thermal properties' 'geodetics' 'tectonics']\n"]}], "source": ["\n", "category_labels = test_csv.Cat3.unique()\n", "print(category_labels)"]}, {"cell_type": "code", "execution_count": 9, "id": "e8e36efc-90da-4cde-a80b-4aed91ec48ec", "metadata": {}, "outputs": [], "source": ["# Create and upload points to Qdrant\n", "points = []\n", "for idx, row in train_csv.iterrows():\n", "    point = models.PointStruct(\n", "        id=idx,  # Use the dataframe index as the point ID\n", "        vector=embeddings[idx],  # Convert the embedding to a list\n", "        payload={'label_2': row['Cat2'] , \"text\":row['Text'],\"Title\":row['Title'],\"label_3\":row['Cat3']}  # Use the label_text as the payload\n", "    )\n", "    points.append(point)\n", "qdrant_client.upload_points(collection_name='text-classification', points=points)"]}, {"cell_type": "code", "execution_count": null, "id": "1ed93823", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["成功获取 10 个文本的嵌入向量。\n", "第一个文本的嵌入向量长度: 1024\n"]}], "source": ["# Create and upload points to Qdrant\n", "embedding_label = get_openai_embeddings(category_labels)\n", "\n", "\n", "if embedding_label:\n", "    print(f\"成功获取 {len(embedding_label)} 个文本的嵌入向量。\")\n", "    # 示例：打印第一个文本的嵌入向量长度\n", "    print(f\"第一个文本的嵌入向量长度: {len(embedding_label[0])}\")\n", "else:\n", "    print(\"未能成功获取嵌入向量。\")\n", "point_label = []\n", "for idx, row in category_labels.iterrows():\n", "    point = models.PointStruct(\n", "        id=idx,  # Use the dataframe index as the point ID\n", "        vector=embedding_label[idx],  # Convert the embedding to a list\n", "        payload={'label': row['label']}  # Use the label_text as the payload\n", "    )\n", "    point_label.append(point)\n", "qdrant_label.upload_points(collection_name='label', points=point_label)"]}, {"cell_type": "code", "execution_count": 12, "id": "5d65f000-0c6f-4f9d-a252-7096b5d81e59", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query Text: Background: Joint pain, including back pain, and arthritis are common conditions in the United States, affecting more than 100 million individuals and costing upwards of $200 billion each year. Although activity limitations associated with these disorders impose a substantial economic burden, this relationship has not been explored in a large U.S. cohort. Methods: In this study, we used the Medical Expenditures Panel Survey to investigate whether functional limitations explain the difference in medical expenditures between patients with arthritis and joint pain and those without. We used sequential explanatory linear models to investigate this relationship and accounted for various covariates. Results: Unadjusted mean expenditures were $10,587 for those with joint pain or arthritis, compared with $3813 for those without. In a fully adjusted model accounting also for functional limitations, those with joint pain or arthritis paid $1638 more than those without, a statistically significant difference. Conclusions: The growing economic and public health burden of arthritis and joint pain, as well as the corresponding complications of functional, activity, and sensory limitations, calls for an interdisciplinary approach and heightened awareness among providers to identify strategies that meet the needs of high-risk patients in order to prevent and delay disease progression.\n", "------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_51936\\**********.py:5: DeprecationWarning: `search` method is deprecated and will be removed in the future. Use `query_points` instead.\n", "  qdrant_client.search(collection_name='text-classification', query_vector=query_vector, limit=8,score_threshold=0.3)\n"]}, {"data": {"text/plain": ["[ScoredPoint(id=2930, version=0, score=0.42177488919964223, payload={'label_2': 'atmosphere', 'text': 'This dataset contains a set of Lagrangian particle dispersion simulations of carbon dioxide concentrations using the FLEXible PARTicle (FLEXPART) model. FLEXPART quantified the source-receptor relationships, so-called \"influence functions\", in a backward mode. The simulations were constructed for five Atmospheric Carbon and Transport America (ACT-America) deployments over the eastern U.S. that occurred in 2016-2019. Each receptor of the influence function is the 30-second or 10-minute interval along flight tracks, characterized by a box with boundaries between the maximum and minimum latitude/longitude as well as between the maximum and minimum altitudes during the interval. Each receptor box released 5,000 particles and simulated their transport and dispersion backward for 10 or 20 days. The simulations were driven by 27-km meteorology provided by the WRF-Chem simulation or by ERA-Interim data from the European Centre for Medium-Range Weather Forecasts (ECMWF). Background levels of carbon dioxide were obtained from CarbonTracker and OCO-2 v9 MIP. The data are provided in netCDF and FLEXPART binary formats.', 'Title': 'FLEXPART Influence Functions for ACT-America, 2016-2017', 'label_3': 'atmospheric chemistry'}, vector=None, shard_key=None, order_value=None),\n", " ScoredPoint(id=3287, version=0, score=0.40538649820487593, payload={'label_2': 'biosphere', 'text': 'In English:\\nAntiparasite treatment to study the effects of gastrointestinal parasites in adults of adelie penguin.\\n\\nEn Espa&amp;#241;ol:\\nTratamiento antiparasitario para estudiar los efectos de los parasitos gastrointestinales en adultos de ping&amp;#252;ino de adelia.', 'Title': 'Antiparasite treatment to study the effects of gastrointestinal parasites in adults of adelie penguin (Tratamiento antiparasitario para estudiar los efectos de los parasitos gastrointestinales en adultos de pingüino de adelia).', 'label_3': 'ecological dynamics'}, vector=None, shard_key=None, order_value=None),\n", " ScoredPoint(id=1351, version=0, score=0.40387163983359387, payload={'label_2': 'cryosphere', 'text': 'Accurate reconstructions and predictions of glacier movement on timescales of human interest require a better understanding of available observations and the ability to model the key processes that govern ice flow.  The fact that many of these processes are interconnected, are loosely constrained by data, and involve not only the ice, but also the atmosphere, ocean, and solid Earth, makes this a challenging endeavor, but one that is essential for Earth-system modeling and the resulting climate and sea-level forecasts that are provided to policymakers worldwide. Based on the amount of ice present in the West Antarctic Ice Sheet and its ability to flow and/or melt into the ocean, its complete collapse would result in a global sea-level rise of 3.3 to 5 meters, making its stability and rate of change scientific questions of global societal significance. Whether or not a collapse eventually occurs, a better understanding of the potential West Antarctic contribution to sea level over the coming decades and centuries is necessary when considering the fate of coastal population centers.  Recent observations of the Amundsen Sea Embayment of West Antarctica indicate that it is experiencing faster mass loss than any other region of the continent.  At present, the long-term stability of this embayment is unknown, with both theory and observations suggesting that collapse is possible.  This study is focused on this critical region as well as processes governing changes in outlet glacier flow.  To this end, we will test an ice-sheet model against existing observations and improve treatment of key processes within ice sheet models.\\n\\nThis is a four-year (one year of no-cost extension) modeling study using the open-source Ice Sheet System Model in coordination with other models to help improve projections of future sea-level change.  Overall project goals, which are distributed across the collaborating institutions, are to:\\n1. hindcast the past two-to-three decades of evolution of the Amundsen Sea Embayment sector to determine controlling processes, incorporate and test parameterizations, and assess and improve model initialization, spinup, and performance;\\n2. utilize observations from glacial settings and efficient process-oriented models to develop a better understanding of key processes associated with outlet glacier dynamics and to create numerically efficient parameterizations for these often sub-grid-scale processes;\\n3. project a range of evolutions of the Amundsen Sea Embayment sector in the next several centuries given various forcings and inclusion or omission of physical processes in the model.', 'Title': 'Collaborative Research: Evaluating Retreat in the Amundsen Sea Embayment: Assessing Controlling Processes, Uncertainties, and Projections', 'label_3': 'glaciers/ice sheets'}, vector=None, shard_key=None, order_value=None),\n", " ScoredPoint(id=509, version=0, score=0.3905910647700673, payload={'label_2': 'human dimensions', 'text': 'The 2015 Urban Extents from VIIRS and MODIS for the Continental U.S. Using Machine Learning Methods data set models urban settlements in the Continental United States (CONUS) as of 2015. When applied to the combination of daytime spectral and nighttime lights satellite data, the machine learning methods achieved high accuracy at an intermediate-resolution of 500 meters at large spatial scales. The input data for these models were two types of satellite imagery: Visible Infrared Imaging Radiometer Suite (VIIRS) Nighttime Light (NTL) data from the Day/Night Band (DNB), and Moderate Resolution Imaging Spectroradiometer (MODIS) corrected daytime Normalized Difference Vegetation Index (NDVI). Although several machine learning methods were evaluated, including Random Forest (RF), Gradient Boosting Machine (GBM), Neural Network (NN), and the Ensemble of RF, GBM, and NN (ESB), the highest accuracy results were achieved with NN, and those results were used to delineate the urban extents in this data set.', 'Title': '2015 Urban Extents from VIIRS and MODIS for the Continental U.S. Using Machine Learning Methods', 'label_3': 'boundaries'}, vector=None, shard_key=None, order_value=None),\n", " ScoredPoint(id=3123, version=0, score=0.38989010060782503, payload={'label_2': 'spectral/engineering', 'text': 'The Advanced Microwave Precipitation Radiometer (AMPR) IMPACTS dataset consists of brightness temperature measurements collected by the Advanced Microwave Precipitation Radiometer (AMPR) onboard the NASA ER-2 high-altitude research aircraft. AMPR provides multi-frequency microwave imagery, with high spatial and temporal resolution for deriving cloud, precipitation, water vapor, and surface properties. These measurements were taken during the Investigation of Microphysics and Precipitation for Atlantic Coast-Threatening Snowstorms (IMPACTS) campaign. Funded by NASA’s Earth Venture program, IMPACTS is the first comprehensive study of East Coast snowstorms in 30 years. Data files are available from December 16, 2019, through March 2, 2023, in netCDF-4 format.  ', 'Title': 'Advanced Microwave Precipitation Radiometer (AMPR) IMPACTS', 'label_3': 'microwave'}, vector=None, shard_key=None, order_value=None),\n", " ScoredPoint(id=349, version=0, score=0.3897186762147772, payload={'label_2': 'solid earth', 'text': \"Part 1: Nontechnical\\n\\n \\n\\nUnlike other locations on the globe Antarctica is not known for having large earthquakes and the remote nature and harsh conditions make it difficult to install and maintain seismometers for earthquake detection.  Some researchers believe the lack of large earthquakes is due to the continent being surrounded by inactive tectonic margins. However, in the last two decades, scientists have discovered that more earthquakes occur in the interior of the continent than previously observed. This suggests that there are many earthquakes missing from historic earthquake catalogs.  This study aims to find the missing earthquakes using novel earthquake detection and location techniques from seismic data collected from temporary and permanent seismic stations in Antarctica over the last 25 years. Locating these earthquakes will help understand if and where earthquakes are located in Antarctica and will help in planning future seismic deployments. As part of the project broader impacts, a field expedition with the Girls on Rock program will be conducted to teach high school age girls, and especially those from underrepresented backgrounds, data visualization techniques using scientific data. \\n\\n \\n\\nPart 2: Technical\\n\\n \\n\\nThe spatial distribution of seismicity and the number of moderate magnitude earthquakes in Antarctica is not well-defined. The current catalog of earthquakes may be biased by uneven and sparse seismograph distribution on the continent. We will mine existing broadband seismic data from both permanent and temporary deployments to lower the earthquake detection threshold across Interior Antarctica, with a focus on tectonic earthquakes. The hypothesis is that Interior Antarctica has abundant moderate magnitude earthquakes, previously undetected. These earthquakes are likely collocated with major tectonic features such as the Transantarctic Mountains, the suspected Vostok collision zone, the West Antarctic Rift System, the crustal compositional boundary between East and West Antarctica, and the Cretaceous East Antarctic Rift. Previous seismic deployments have recorded earthquakes in the Antarctic interior, suggesting there are many earthquakes missing from the current catalog. We propose to use novel earthquake location techniques designed for automated detection and location using 25 years of continuous data archived at IRIS from PASSCAL experiments and permanent stations. The approach will use STA/LTA detectors on the first arrival P-wave to 90 degrees distance, Reverse Time Imaging to locate events, and beamforming at dense arrays strategically located on cratons for enhanced detection and location. The combination of detection and location techniques used in this work has not been used on teleseismic body waves, although similar methods have worked well for surface wave studies. If successful the project would provide an excellent training dataset for future scrutiny of newly discovered Antarctic seismicity with machine learning approaches and/or new targeted data collection. We plan to collaborate with Girls on Rock, a local and international organization committed to building a culturally diverse community in science, art, and wilderness exploration, in a summer field expedition and integrating computer coding into post-field scientific projects.\\n\\n\\n\\nThis award reflects NSF's statutory mission and has been deemed worthy of support through evaluation using the Foundation's intellectual merit and broader impacts review criteria.\", 'Title': 'EAGER: Lowering the detection threshold of Antarctic seismicity to reveal undiscovered intraplate deformation', 'label_3': 'tectonics'}, vector=None, shard_key=None, order_value=None),\n", " ScoredPoint(id=597, version=0, score=0.38957033967151017, payload={'label_2': 'cryosphere', 'text': 'This award supports a project to reconstruct past ice-surface elevations from detailed glacial mapping and dating of moraines (using 14C dates of algae from former ice-marginal ponds and 10Be surface exposure ages) in the region of the Darwin-Hatherton Glaciers in Antarctica in order to try and resolve very different interpretations that currently exist about the glacial history in the region. The results will be integrated with existing climate and geophysical data into a flow-line model to gain insight into glacier response to climate and ice-dynamics perturbations during the Late Glacial Maximum (LGM) in Antarctica. The work will contribute to a better understanding of both LGM ice thickness and whether or not there is any evidence that Antarctica contributed to Meltwater Pulse (MWP)-1A a very controversial topic in Antarctic glacial geology. The intellectual merit of the work relates to the fact that reconstructing past fluctuations of the Antarctic Ice Sheet (AIS) is critical for understanding the sensitivity of ice volume to sea-level and climatic change. Constraints on past behavior help put ongoing changes into context and provide a basis for predicting future sea-level rise. Broader impacts include the support of two graduate and two undergraduate students, as well as a female early-career investigator. Graduate students will be involved in all stages of the project from planning and field mapping to geochronological analyses, interpretation, synthesis and reporting. Two undergraduates will work on lab-based research from the project. The project also will include visits to K-12 classrooms to talk about glaciers and climate change, correspondence with teachers and students from the field, and web-based outreach. This award has field work in Antarctica.', 'Title': 'Collaborative Research: Assessing the Antarctic Contribution to Sea-level Changes during the Last Deglaciation: Constraints from Darwin Glacier', 'label_3': 'glaciers/ice sheets'}, vector=None, shard_key=None, order_value=None),\n", " ScoredPoint(id=1056, version=0, score=0.3841894192317431, payload={'label_2': 'oceans', 'text': 'A monthly mean climatological wind stress was derived from the             National Climate Center TDF-11 ocean observations. The dataset was             derived in 1989 using the Large and Pond algorithm for ocean wind             stress.', 'Title': 'Climatological Mean Global Wind Stress and Curl, by <PERSON> et al.', 'label_3': 'ocean winds'}, vector=None, shard_key=None, order_value=None)]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["query_text = \"Background: Joint pain, including back pain, and arthritis are common conditions in the United States, affecting more than 100 million individuals and costing upwards of $200 billion each year. Although activity limitations associated with these disorders impose a substantial economic burden, this relationship has not been explored in a large U.S. cohort. Methods: In this study, we used the Medical Expenditures Panel Survey to investigate whether functional limitations explain the difference in medical expenditures between patients with arthritis and joint pain and those without. We used sequential explanatory linear models to investigate this relationship and accounted for various covariates. Results: Unadjusted mean expenditures were $10,587 for those with joint pain or arthritis, compared with $3813 for those without. In a fully adjusted model accounting also for functional limitations, those with joint pain or arthritis paid $1638 more than those without, a statistically significant difference. Conclusions: The growing economic and public health burden of arthritis and joint pain, as well as the corresponding complications of functional, activity, and sensory limitations, calls for an interdisciplinary approach and heightened awareness among providers to identify strategies that meet the needs of high-risk patients in order to prevent and delay disease progression.\" #test_csv.iloc[4]['text']\n", "print(f\"Query Text: {query_text}\")\n", "print(\"---\"*80)\n", "query_vector = get_embeddings(query_text)\n", "qdrant_client.search(collection_name='text-classification', query_vector=query_vector, limit=8,score_threshold=0.3)"]}, {"cell_type": "code", "execution_count": 10, "id": "5a92ae19-d958-4ad9-8f6f-9d7b8fa3d60f", "metadata": {}, "outputs": [], "source": ["def qdrant_search(query_text,top_k=5):\n", "    query_vector =get_embeddings(query_text)\n", "    search_response = qdrant_client.search(collection_name='text-classification', query_vector=query_vector, limit=top_k)\n", "    return search_response"]}, {"cell_type": "code", "execution_count": 13, "id": "46d538da", "metadata": {}, "outputs": [], "source": ["def label_search(query_text,top_k=3):\n", "    query_vector =get_embeddings(query_text)\n", "    search_response = qdrant_label.search(collection_name='label', query_vector=query_vector, limit=top_k)\n", "    return search_response"]}, {"cell_type": "code", "execution_count": 11, "id": "059bb5f2", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Building prefix dict from the default dictionary ...\n", "Loading model from cache C:\\Users\\<USER>\\AppData\\Local\\Temp\\jieba.cache\n", "Loading model cost 0.461 seconds.\n", "Prefix dict has been built successfully.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Rank 1:\n", "Document: Antiparasite treatment to study the effects of gastrointestinal parasites in adults of adelie penguin (Tratamiento antiparasitario para estudiar los efectos de los parasitos gastrointestinales en adultos de pingüino de adelia).\n", "Label_2: biosphere\n", "Label_3: ecological dynamics\n", "BM25 Score: 823.7126\n", "Vector Score: 0.4054\n", "Combined Score: 1.2291\n", "\n", "Rank 2:\n", "Document: Collaborative Research: Evaluating Retreat in the Amundsen Sea Embayment: Assessing Controlling Processes, Uncertainties, and Projections\n", "Label_2: cryosphere\n", "Label_3: glaciers/ice sheets\n", "BM25 Score: 813.9910\n", "Vector Score: 0.4039\n", "Combined Score: 1.2179\n", "\n", "Rank 3:\n", "Document: FLEXPART Influence Functions for ACT-America, 2016-2017\n", "Label_2: atmosphere\n", "Label_3: atmospheric chemistry\n", "BM25 Score: 736.4063\n", "Vector Score: 0.4218\n", "Combined Score: 1.1582\n", "\n"]}, {"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20908\\2090503538.py:42: DeprecationWarning: `search` method is deprecated and will be removed in the future. Use `query_points` instead.\n", "  vector_results = qdrant_client.search(\n"]}], "source": ["from rank_bm25 import BM25Okapi\n", "import jieba\n", "\n", "\n", "# 从 Qdrant 获取文档数据并构建 BM25 索引\n", "def build_bm25_index():\n", "    # 从 Qdrant 滚动获取所有文档\n", "    documents = []\n", "    scroll_result = qdrant_client.scroll(\n", "        collection_name=\"text-classification\",\n", "        with_payload=True,  # 获取文档内容\n", "        limit=100  # 每次加载的文档数量，可调整\n", "    )\n", "    while scroll_result:\n", "        for point in scroll_result[0]:  # 获取点的列表\n", "            documents.append(point.payload[\"Title\"])\n", "        if scroll_result[1] is None:  # 如果没有更多数据\n", "            break\n", "        scroll_result = qdrant_client.scroll(\n", "            collection_name=\"text-classification\",\n", "            with_payload=True,\n", "            limit=100,\n", "            offset=scroll_result[1]  # 获取下一个偏移量\n", "        )\n", "\n", "    # 构建 BM25 索引\n", "    tokenized_corpus = [list(jieba.cut(doc)) for doc in documents]\n", "    bm25 = BM25Okapi(tokenized_corpus)\n", "    return bm25, documents\n", "\n", "# 加载 BM25 索引\n", "bm25, documents = build_bm25_index()\n", "\n", "# 混合检索函数\n", "def hybrid_search(query_text, top_k=5):\n", "    # Step 1: 使用 BM25 检索\n", "    tokenized_query = list(jieba.cut(query_text))\n", "    bm25_scores = bm25.get_scores(tokenized_query)\n", "\n", "    # Step 2: 使用 Qdrant 向量搜索\n", "    query_vector = get_embeddings(query_text)  # 替换为你的向量化模型\n", "    vector_results = qdrant_client.search(\n", "        collection_name=\"text-classification\",\n", "        query_vector=query_vector,\n", "        limit=top_k\n", "    )\n", "\n", "    # Step 3: 综合 BM25 和向量分数\n", "    results = []\n", "    for res in vector_results:\n", "        doc_text = res.payload[\"Title\"]  # 根据文档内容查找索引\n", "        label_2=res.payload[\"label_2\"]\n", "        label_3=res.payload[\"label_3\"]\n", "        bm25_idx = documents.index(doc_text)  # 动态获取 BM25 索引\n", "        bm25_score = bm25_scores[bm25_idx]\n", "        combined_score = bm25_score*0.001 + res.score\n", "        results.append({\n", "            \"text\": doc_text,\n", "            \"label_2\":label_2,\n", "            \"label_3\":label_3,\n", "            \"bm25_score\": bm25_score,\n", "            \"vector_score\": res.score,\n", "            \"combined_score\": combined_score\n", "        })\n", "\n", "    # 按综合分数排序\n", "    results = sorted(results, key=lambda x: x[\"combined_score\"], reverse=True)\n", "\n", "    return results[:top_k]\n", "\n", "# 测试检索\n", "query_text = \"Background: Joint pain, including back pain, and arthritis are common conditions in the United States, affecting more than 100 million individuals and costing upwards of $200 billion each year. Although activity limitations associated with these disorders impose a substantial economic burden, this relationship has not been explored in a large U.S. cohort. Methods: In this study, we used the Medical Expenditures Panel Survey to investigate whether functional limitations explain the difference in medical expenditures between patients with arthritis and joint pain and those without. We used sequential explanatory linear models to investigate this relationship and accounted for various covariates. Results: Unadjusted mean expenditures were $10,587 for those with joint pain or arthritis, compared with $3813 for those without. In a fully adjusted model accounting also for functional limitations, those with joint pain or arthritis paid $1638 more than those without, a statistically significant difference. Conclusions: The growing economic and public health burden of arthritis and joint pain, as well as the corresponding complications of functional, activity, and sensory limitations, calls for an interdisciplinary approach and heightened awareness among providers to identify strategies that meet the needs of high-risk patients in order to prevent and delay disease progression.\"\n", "results = hybrid_search(query_text, top_k=3)\n", "\n", "# 输出结果\n", "for idx, result in enumerate(results):\n", "    print(f\"Rank {idx + 1}:\")\n", "    print(f\"Document: {result['text']}\")\n", "    print(f\"Label_2: {result['label_2']}\")\n", "    print(f\"Label_3: {result['label_3']}\")\n", "    print(f\"BM25 Score: {result['bm25_score']:.4f}\")\n", "    print(f\"Vector Score: {result['vector_score']:.4f}\")\n", "    print(f\"Combined Score: {result['combined_score']:.4f}\\n\")"]}, {"cell_type": "code", "execution_count": 15, "id": "ca896f4b-0705-415d-9220-c21549eff1b7", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_51936\\**********.py:3: DeprecationWarning: `search` method is deprecated and will be removed in the future. Use `query_points` instead.\n", "  search_response = qdrant_client.search(collection_name='text-classification', query_vector=query_vector, limit=top_k)\n"]}, {"data": {"text/plain": ["[ScoredPoint(id=2930, version=0, score=0.42177487624694016, payload={'label_2': 'atmosphere', 'text': 'This dataset contains a set of Lagrangian particle dispersion simulations of carbon dioxide concentrations using the FLEXible PARTicle (FLEXPART) model. FLEXPART quantified the source-receptor relationships, so-called \"influence functions\", in a backward mode. The simulations were constructed for five Atmospheric Carbon and Transport America (ACT-America) deployments over the eastern U.S. that occurred in 2016-2019. Each receptor of the influence function is the 30-second or 10-minute interval along flight tracks, characterized by a box with boundaries between the maximum and minimum latitude/longitude as well as between the maximum and minimum altitudes during the interval. Each receptor box released 5,000 particles and simulated their transport and dispersion backward for 10 or 20 days. The simulations were driven by 27-km meteorology provided by the WRF-Chem simulation or by ERA-Interim data from the European Centre for Medium-Range Weather Forecasts (ECMWF). Background levels of carbon dioxide were obtained from CarbonTracker and OCO-2 v9 MIP. The data are provided in netCDF and FLEXPART binary formats.', 'Title': 'FLEXPART Influence Functions for ACT-America, 2016-2017', 'label_3': 'atmospheric chemistry'}, vector=None, shard_key=None, order_value=None),\n", " ScoredPoint(id=3287, version=0, score=0.40538653108181055, payload={'label_2': 'biosphere', 'text': 'In English:\\nAntiparasite treatment to study the effects of gastrointestinal parasites in adults of adelie penguin.\\n\\nEn Espa&amp;#241;ol:\\nTratamiento antiparasitario para estudiar los efectos de los parasitos gastrointestinales en adultos de ping&amp;#252;ino de adelia.', 'Title': 'Antiparasite treatment to study the effects of gastrointestinal parasites in adults of adelie penguin (Tratamiento antiparasitario para estudiar los efectos de los parasitos gastrointestinales en adultos de pingüino de adelia).', 'label_3': 'ecological dynamics'}, vector=None, shard_key=None, order_value=None),\n", " ScoredPoint(id=1351, version=0, score=0.40387163983359387, payload={'label_2': 'cryosphere', 'text': 'Accurate reconstructions and predictions of glacier movement on timescales of human interest require a better understanding of available observations and the ability to model the key processes that govern ice flow.  The fact that many of these processes are interconnected, are loosely constrained by data, and involve not only the ice, but also the atmosphere, ocean, and solid Earth, makes this a challenging endeavor, but one that is essential for Earth-system modeling and the resulting climate and sea-level forecasts that are provided to policymakers worldwide. Based on the amount of ice present in the West Antarctic Ice Sheet and its ability to flow and/or melt into the ocean, its complete collapse would result in a global sea-level rise of 3.3 to 5 meters, making its stability and rate of change scientific questions of global societal significance. Whether or not a collapse eventually occurs, a better understanding of the potential West Antarctic contribution to sea level over the coming decades and centuries is necessary when considering the fate of coastal population centers.  Recent observations of the Amundsen Sea Embayment of West Antarctica indicate that it is experiencing faster mass loss than any other region of the continent.  At present, the long-term stability of this embayment is unknown, with both theory and observations suggesting that collapse is possible.  This study is focused on this critical region as well as processes governing changes in outlet glacier flow.  To this end, we will test an ice-sheet model against existing observations and improve treatment of key processes within ice sheet models.\\n\\nThis is a four-year (one year of no-cost extension) modeling study using the open-source Ice Sheet System Model in coordination with other models to help improve projections of future sea-level change.  Overall project goals, which are distributed across the collaborating institutions, are to:\\n1. hindcast the past two-to-three decades of evolution of the Amundsen Sea Embayment sector to determine controlling processes, incorporate and test parameterizations, and assess and improve model initialization, spinup, and performance;\\n2. utilize observations from glacial settings and efficient process-oriented models to develop a better understanding of key processes associated with outlet glacier dynamics and to create numerically efficient parameterizations for these often sub-grid-scale processes;\\n3. project a range of evolutions of the Amundsen Sea Embayment sector in the next several centuries given various forcings and inclusion or omission of physical processes in the model.', 'Title': 'Collaborative Research: Evaluating Retreat in the Amundsen Sea Embayment: Assessing Controlling Processes, Uncertainties, and Projections', 'label_3': 'glaciers/ice sheets'}, vector=None, shard_key=None, order_value=None),\n", " ScoredPoint(id=509, version=0, score=0.3905910647700673, payload={'label_2': 'human dimensions', 'text': 'The 2015 Urban Extents from VIIRS and MODIS for the Continental U.S. Using Machine Learning Methods data set models urban settlements in the Continental United States (CONUS) as of 2015. When applied to the combination of daytime spectral and nighttime lights satellite data, the machine learning methods achieved high accuracy at an intermediate-resolution of 500 meters at large spatial scales. The input data for these models were two types of satellite imagery: Visible Infrared Imaging Radiometer Suite (VIIRS) Nighttime Light (NTL) data from the Day/Night Band (DNB), and Moderate Resolution Imaging Spectroradiometer (MODIS) corrected daytime Normalized Difference Vegetation Index (NDVI). Although several machine learning methods were evaluated, including Random Forest (RF), Gradient Boosting Machine (GBM), Neural Network (NN), and the Ensemble of RF, GBM, and NN (ESB), the highest accuracy results were achieved with NN, and those results were used to delineate the urban extents in this data set.', 'Title': '2015 Urban Extents from VIIRS and MODIS for the Continental U.S. Using Machine Learning Methods', 'label_3': 'boundaries'}, vector=None, shard_key=None, order_value=None),\n", " ScoredPoint(id=3123, version=0, score=0.38989008598189, payload={'label_2': 'spectral/engineering', 'text': 'The Advanced Microwave Precipitation Radiometer (AMPR) IMPACTS dataset consists of brightness temperature measurements collected by the Advanced Microwave Precipitation Radiometer (AMPR) onboard the NASA ER-2 high-altitude research aircraft. AMPR provides multi-frequency microwave imagery, with high spatial and temporal resolution for deriving cloud, precipitation, water vapor, and surface properties. These measurements were taken during the Investigation of Microphysics and Precipitation for Atlantic Coast-Threatening Snowstorms (IMPACTS) campaign. Funded by NASA’s Earth Venture program, IMPACTS is the first comprehensive study of East Coast snowstorms in 30 years. Data files are available from December 16, 2019, through March 2, 2023, in netCDF-4 format.  ', 'Title': 'Advanced Microwave Precipitation Radiometer (AMPR) IMPACTS', 'label_3': 'microwave'}, vector=None, shard_key=None, order_value=None)]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["qdrant_search(\"Background: Joint pain, including back pain, and arthritis are common conditions in the United States, affecting more than 100 million individuals and costing upwards of $200 billion each year. Although activity limitations associated with these disorders impose a substantial economic burden, this relationship has not been explored in a large U.S. cohort. Methods: In this study, we used the Medical Expenditures Panel Survey to investigate whether functional limitations explain the difference in medical expenditures between patients with arthritis and joint pain and those without. We used sequential explanatory linear models to investigate this relationship and accounted for various covariates. Results: Unadjusted mean expenditures were $10,587 for those with joint pain or arthritis, compared with $3813 for those without. In a fully adjusted model accounting also for functional limitations, those with joint pain or arthritis paid $1638 more than those without, a statistically significant difference. Conclusions: The growing economic and public health burden of arthritis and joint pain, as well as the corresponding complications of functional, activity, and sensory limitations, calls for an interdisciplinary approach and heightened awareness among providers to identify strategies that meet the needs of high-risk patients in order to prevent and delay disease progression.\")"]}, {"cell_type": "code", "execution_count": 12, "id": "bdc5c4a2", "metadata": {}, "outputs": [], "source": ["# 检索最近的100个，再通过加权计算出最前的两个标签\n", "def retrieve_and_weight_labels(query_text,top_k=5,levels=\"label_3\"):\n", "    # 在 Qdrant 中搜索最近的100个匹配\n", "    search_results = hybrid_search(query_text,top_k)\n", "    \n", "    # 检查是否有结果\n", "    if not search_results:\n", "        return []\n", "    \n", "    # 创建一个字典来存储标签和它们的加权分数\n", "    label_scores = {}\n", "    \n", "    # 遍历搜索结果并计算加权分数\n", "    for result in search_results:\n", "        label = result[levels]\n", "        score = result['combined_score']\n", "        \n", "        if label in label_scores:\n", "            label_scores[label] += score\n", "        else:\n", "            label_scores[label] = score\n", "    \n", "    # 将字典转换为列表并按分数排序\n", "    sorted_labels = sorted(label_scores.items(), key=lambda item: item[1], reverse=True)\n", "    \n", "    # 返回前两个标签\n", "    return [label for label, score in sorted_labels]"]}, {"cell_type": "code", "execution_count": null, "id": "26562ab0-0583-40e8-86ee-ec4ac505577f", "metadata": {}, "outputs": [], "source": ["categories_list = \"- \" + \"\\n- \".join(category_labels)\n", "\n", "system_prompt = f\"\"\"\n", "你是一个智能AI助理,需要进行一些数据进行分类\\n\n", "对文本信息进行分类，分类标准严格参考下面所给标准，只能从其中选择一个：\n", "{categories_list}\n", "\"\"\"\n", "'''\n", "system_prompt = f\"\"\"\n", "    You are an intelligent AI assistant and need to classify some data.\\n\n", "    Classify the text information, and the classification criteria should be strictly referred to the standards given below. \\n\n", "    You can only choose one from them:\n", "    {categories_list}\n", "    \"\"\"\n", "'''\n", "#结合RAG给出的5个相似样本和他们的标签,对文本信息进行分类，打一个0到1的分数，1表示完全匹配，0表示完全不匹配,分类标准严格参考下面所给标准，只能从其中选择一个：\n", "#{categories_list}\n", "print(system_prompt)"]}, {"cell_type": "code", "execution_count": 19, "id": "f5e430e1", "metadata": {}, "outputs": [], "source": ["import random\n", "\n", "def random_qdrant_search(query_text, top_k=5):\n", "    # 获取集合中的总记录数\n", "    collection_info = qdrant_client.get_collection('text-classification')\n", "    total_points = collection_info.points_count\n", "    \n", "    # 生成随机的记录ID列表\n", "    random_ids = random.sample(range(total_points), min(top_k, total_points))\n", "    \n", "    # 获取随机选择的记录\n", "    search_response = qdrant_client.retrieve(\n", "        collection_name='text-classification',\n", "        ids=random_ids\n", "    )\n", "    \n", "    return search_response"]}, {"cell_type": "code", "execution_count": 20, "id": "e78b037e", "metadata": {}, "outputs": [], "source": ["\n", "def generate_cot_prompt(query_text,top_k=5):\n", "    \n", "    #labels= retrieve_and_weight_labels(query_text,top_k)\n", "    \n", "    if top_k==20:\n", "       labels=category_labels\n", "    else:\n", "       labels= retrieve_and_weight_labels(query_text,top_k) \n", "    prompt = f\"\"\"\n", "    你是一个智能AI助理,需要进行一些数据进行分类\\n\n", "    结合给出的相似样本,对文本信息进行分类，分类标准严格参考下面所给标签和详细的标签说明，只能从其中选择一个\\n\\n\n", "    \"\"\"\n", "    # 初始化一个计数器来给所有标签统一编号\n", "    counter = 1\n", "\n", "    for label in labels:\n", "        label_embedding = get_embeddings(label)\n", "        search_response = qdrant_label.search(\n", "            collection_name='label',\n", "            query_vector=label_embedding,\n", "            limit=1\n", "        )\n", "        \n", "        sample_documents = [\n", "            {\n", "                \"标签\": result.payload['label'],\n", "                \"定义\": result.payload['definition'],\n", "                \"样例\": result.payload['example'],\n", "                \"关键字\": result.payload['keyword'],\n", "            } for result in search_response\n", "        ]\n", "        \n", "        for doc in sample_documents:\n", "            # 编号. 标签\n", "            prompt += f\"{counter}. {doc['标签']}\\n\"\n", "            # 定义：定义内容\n", "            prompt += f\"定义：{doc['定义']}\\n\"\n", "            # 示例：\n", "            prompt += \"样例:\\n\"\n", "            # 将 \"样例\" 字符串拆分成列表，并逐行添加\n", "            examples_list = doc['样例'].split('; ')\n", "            for example in examples_list:\n", "                prompt += f\"- {example}\\n\"\n", "            # 关键词：关键词字符串\n", "            prompt += f\"关键词：{doc['关键字']}\\n\\n\"\n", "            counter += 1\n", "\n", "    return prompt"]}, {"cell_type": "code", "execution_count": 13, "id": "652f97ae", "metadata": {}, "outputs": [], "source": ["client = OpenAI(api_key=\"sk-aoxqlpkmxqupdfzxrquoxcizkpjgrkorieayumjqvxxhmghw\",base_url=\"https://api.siliconflow.cn/v1\")\n", "#LLM_Name=\"Qwen/Qwen2.5-7B-Instruct\"\n", "LLM_Name=\"Qwen/Qwen3-8B\""]}, {"cell_type": "code", "execution_count": 14, "id": "8746411b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✓ 图数据库连接成功\n"]}], "source": ["from graph_db import GraphDB\n", "try:\n", "    graph_db = GraphDB()\n", "    print(\"✓ 图数据库连接成功\")\n", "except Exception as e:\n", "    print(f\"✗ 图数据库连接失败: {e}\")\n", "\n", "# 获取完整知识图谱结构\n", "def get_full_knowledge_graph():\n", "    \"\"\"获取完整的知识图谱结构\"\"\"\n", "    try:\n", "        # 获取所有路径\n", "        query = \"\"\"\n", "        MATCH (l1:Category1)-[:contains]->(l2:Category2)-[:contains]->(l3:Category3)\n", "        RETURN l1.name as l1, l2.name as l2, l3.name as l3\n", "        \"\"\"\n", "        result = graph_db._query_database(query)\n", "        \n", "        paths = []\n", "        for record in result.records:\n", "            path = f\"{record['l2']} -> {record['l3']}\"\n", "            paths.append(path)\n", "        \n", "        return paths\n", "    except Exception as e:\n", "        print(f\"获取知识图谱失败: {e}\")\n", "        return []\n", "    \n", "kg_context = get_full_knowledge_graph()"]}, {"cell_type": "code", "execution_count": 43, "id": "dbe5bc02-56f2-471c-a38c-270b68fd2ba5", "metadata": {}, "outputs": [], "source": ["import re\n", "import time\n", "\n", "def classify_query_text(query_text, text_top=4, label_top=5) -> str:\n", "    # Search the Qdrant For Related Documents\n", "    search_results = qdrant_search(query_text, text_top)\n", "    # Prepare the Sample Documents retrieved from RAG\n", "    sample_documents = [\n", "        {\n", "            \"Title\": result.payload['Title'],\n", "            \"Label\": result.payload['label_3'],\n", "        } for result in search_results\n", "    ]\n", "    \n", "    #system_prompt = generate_cot_prompt(query_text,label_top)\n", "    # Prepare the User Message, append the sample documents to the user message\n", "    category_label_3 = test_csv.Cat3.unique()\n", "    if label_top==20:\n", "        categories_list = \"- \" + \"\\n- \".join(category_label_3) \n", "\n", "    else:\n", "        #labels= retrieve_and_weight_labels(query_text,label_top)\n", "        #categories_list = \"- \" + \"\\n- \".join(labels)\n", "        categories_list = \"- \" + \"\\n- \".join(retrieve_and_weight_labels(query_text,label_top))\n", "    \n", "    system_prompt = f\"\"\"\n", "        你是一个智能AI助理,需要进行一些数据进行分类\\n\n", "        对文本信息进行分类，分类标准严格参考下面所给标准，只能从其中选择一个：\n", "        {categories_list}\n", "        \"\"\"  \n", "    \n", "    prompt = (\n", "        #f\"参考样本：{sample_documents}\\n\"\n", "        f\"文本内容：{query_text}\\n\"\n", "        \"请你根据文本内容和提供的标签，选择一个最合适的标签，只在所给标签中选择\"\n", "        \"请仅返回对应的标签，不做任何解释。\"\n", "    ) \n", "    \n", "\n", "    messages = [\n", "     {\"role\": \"system\", \"content\": system_prompt},\n", "     {\"role\": \"user\", \"content\": prompt}\n", "      ]\n", "    while True:\n", "        try:\n", "            response = client.chat.completions.create(\n", "                model=LLM_Name,\n", "                temperature=1.0,\n", "                messages=messages\n", "            )\n", "            #time.sleep(0.2)  # 等待 1 分钟后重试\n", "            response = response.choices[0].message.content\n", "            return response\n", "        except Exception as e:\n", "            time.sleep(60)\n", "            response = client.chat.completions.create(\n", "                model=LLM_Name,\n", "                temperature=1.0,\n", "                messages=messages\n", "            )\n", "            #time.sleep(0.2)  # 等待 1 分钟后重试\n", "            response = response.choices[0].message.content\n", "            return response\n", "     "]}, {"cell_type": "code", "execution_count": 22, "id": "aa941ccd-de8a-447a-a503-5874af9e5f99", "metadata": {}, "outputs": [], "source": ["import re\n", "import time\n", "\n", "def classify_query_label_3(query_text, labels=None, levels=\"Cat3\", text_top=4, label_top=20) -> str:\n", "    # Search the Qdrant For Related Documents\n", "    search_results = qdrant_search(query_text, text_top)\n", "    # Prepare the Sample Documents retrieved from RAG\n", "    if levels==\"Cat3\":\n", "        label_key = 'label_3'\n", "    elif levels==\"Cat2\":\n", "        label_key = 'label_2'\n", "\n", "\n", "    # sample_documents = [\n", "    #     {\n", "    #         \"Title\": result.payload['Title'],\n", "    #         \"Label\": result.payload[label_key],\n", "    #     } for result in search_results\n", "    # ]\n", "    \n", "    # if labels:\n", "    #     categories_list = \"- \" + \"\\n- \".join(labels)\n", "\n", "    # else: \n", "    #     if label_top==20:\n", "    #         categories_list = \"- \" + \"\\n- \".join(test_csv[levels].unique())\n", "    #     else:\n", "    #         categories_list = \"- \" + \"\\n- \".join(retrieve_and_weight_labels(query_text,label_top,label_key))\n", "\n", "    if labels:\n", "        categories_list = labels\n", "\n", "    else: \n", "        if label_top==20:\n", "            categories_list = test_csv[levels].unique().tolist()\n", "        else:\n", "            categories_list = retrieve_and_weight_labels(query_text,label_top,label_key)\n", "    # categories_list = \"- \" + \"\\n- \".join(labels)\n", "    \n", "    # system_prompt = f\"\"\"\n", "    #     你是一个智能AI助理,需要进行一些数据进行分类\\n\n", "    #     对文本信息进行分类，分类标准严格参考下面所给标准，只能从其中选择一个：\n", "    #     {categories_list}\n", "    #     \"\"\"  \n", "    \n", "    # prompt = (\n", "    #     f\"参考样本：{sample_documents}\\n\"\n", "    #     f\"文本内容：{query_text}\\n\"\n", "    #     \"请你根据文本内容和提供的标签，选择一个最合适的标签，只在所给标签中选择\"\n", "    #     \"请仅返回对应的标签，不做任何解释,不做过多的思考。\"\n", "    # ) \n", "\n", "                # Knowledge Graph Context:\n", "                # {kg_context}\n", "    system_prompt = \"You are a NASA Earth science dataset classifier. Output only the exact category name.\\nPlease provide the final answer directly without showing the reasoning process.\"\n", "\n", "    # prompt = f\"\"\"Classify this NASA Earth science dataset into one of these topic categories: {', '.join(categories_list)}.\n", "\n", "\n", "    #             Dataset:\n", "    #             {query_text}\n", "    #             You must output ONLY the specific term from the list above, nothing else. Do not add explanations or additional text.\n", "    #             Topic Category:\"\"\"\n", "                # L3级别预测\n", "    prompt = f\"\"\"Classify this NASA Earth science dataset into one of these specific terms: {', '.join(categories_list)}.\n", "\n", "    Dataset:\n", "    {query_text}\n", "\n", "    You must output ONLY the specific term from the list above, nothing else. Do not add explanations or additional text.\n", "\n", "    Topic Category:\"\"\"\n", "    messages = [\n", "     {\"role\": \"system\", \"content\": system_prompt},\n", "     {\"role\": \"user\", \"content\": prompt}\n", "      ]\n", "    while True:\n", "        try:\n", "            response = client.chat.completions.create(\n", "                model=LLM_Name,\n", "                temperature=1.0,\n", "                messages=messages\n", "            )\n", "            #time.sleep(0.2)  # 等待 1 分钟后重试\n", "            response = response.choices[0].message.content\n", "            return response\n", "        except Exception as e:\n", "            time.sleep(60)\n", "            response = client.chat.completions.create(\n", "                model=LLM_Name,\n", "                temperature=1.0,\n", "                messages=messages\n", "            )\n", "            #time.sleep(0.2)  # 等待 1 分钟后重试\n", "            response = response.choices[0].message.content\n", "            if response :\n", "                response = response.strip().lower()\n", "            if response is None:\n", "                response = random.choice(categories_list)\n", "            return response\n", "     "]}, {"cell_type": "code", "execution_count": 45, "id": "88d4fc77", "metadata": {}, "outputs": [], "source": ["import re\n", "import time\n", "\n", "def classify_query_label_1(query_text, text_top=4, label_top=5) -> str:\n", "    # Search the Qdrant For Related Documents\n", "    search_results = qdrant_search(query_text, text_top)\n", "    # Prepare the Sample Documents retrieved from RAG\n", "    sample_documents = [\n", "        {\n", "            \"Title\": result.payload['Title'],\n", "            \"Label\": result.payload['label_2'],\n", "        } for result in search_results\n", "    ]\n", "    \n", "    #system_prompt = generate_cot_prompt(query_text,label_top)\n", "    # Prepare the User Message, append the sample documents to the user message\n", "    category_label_2=test_csv.Cat2.unique()\n", "    if label_top==20:\n", "        categories_list = \"- \" + \"\\n- \".join(category_label_2) \n", "\n", "    else:\n", "        #labels= retrieve_and_weight_labels(query_text,label_top)\n", "        #categories_list = \"- \" + \"\\n- \".join(labels)\n", "        categories_list = \"- \" + \"\\n- \".join(retrieve_and_weight_labels(query_text,label_top))\n", "    \n", "    system_prompt = f\"\"\"\n", "        你是一个智能AI助理,需要进行一些数据进行分类\\n\n", "        对文本信息进行分类，分类标准严格参考下面所给标准，只能从其中选择一个：\n", "        {categories_list}\n", "        \"\"\"  \n", "    \n", "    prompt = (\n", "        #f\"参考样本：{sample_documents}\\n\"\n", "        f\"文本内容：{query_text}\\n\"\n", "        \"请你根据文本内容和提供的标签，选择一个最合适的标签，只在所给标签中选择\"\n", "        \"请仅返回对应的标签，不做任何解释。\"\n", "    ) \n", "    \n", "\n", "    messages = [\n", "     {\"role\": \"system\", \"content\": system_prompt},\n", "     {\"role\": \"user\", \"content\": prompt}\n", "      ]\n", "    while True:\n", "        try:\n", "            response = client.chat.completions.create(\n", "                model=LLM_Name,\n", "                temperature=1.0,\n", "                messages=messages\n", "            )\n", "            #time.sleep(0.2)  # 等待 1 分钟后重试\n", "            response = response.choices[0].message.content\n", "            return response\n", "        except Exception as e:\n", "            time.sleep(60)\n", "            response = client.chat.completions.create(\n", "                model=LLM_Name,\n", "                temperature=1.0,\n", "                messages=messages\n", "            )\n", "            #time.sleep(0.2)  # 等待 1 分钟后重试\n", "            response = response.choices[0].message.content\n", "            return response\n", "     "]}, {"cell_type": "code", "execution_count": 23, "id": "798dc976", "metadata": {}, "outputs": [], "source": ["\n", "import re\n", "import difflib\n", "import random\n", "# 定义替换列表\n", "'''\n", "replacement_list = ['地形地貌数据', '遥感样本数据', '大气与海洋数据', '遥感反演数据产品', '地面监测数据', '社会经济数据',\n", "                    '遥感解译数据产品', '基础地理数据', '光学数据产品', '遥感应用数据产品']\n", "\n", "'''\n", "replacement_list = category_labels\n", "\n", "\n", "def label_rectify(label):\n", "    label_embedding = get_embeddings(label)\n", "    search_response = qdrant_label.search(\n", "        collection_name='label',\n", "        query_vector=label_embedding,\n", "        limit=1\n", "    )\n", "    sample_documents = [\n", "        {\n", "            \"标签\": result.payload.get('label'),\n", "        } for result in search_response\n", "    ]\n", "    if sample_documents:\n", "        return sample_documents[0][\"标签\"]\n", "    return None\n", "def process_labels(labels):\n", "    new_labels = []\n", "    for label in labels:\n", "        # 去除数字前缀\n", "        label = re.sub(r'^\\d+\\.\\s*', '', label)\n", "        # 处理空数据\n", "        if not label.strip():\n", "            label = random.choice(replacement_list)\n", "        else:\n", "            try:\n", "                # 尝试将数字替换为对应标签\n", "                num = int(label)\n", "                if 1 <= num <= len(replacement_list):\n", "                    label = replacement_list[num - 1]\n", "            except ValueError:\n", "                # 若不是数字，按相似性替换\n", "                if label not in replacement_list:\n", "                    # 查找最相似的标签\n", "                    search_result = label_rectify(label)\n", "                    if search_result:\n", "                        label = search_result\n", "        new_labels.append(label)\n", "    return new_labels\n"]}, {"cell_type": "code", "execution_count": 21, "id": "cbf7a22e-ba34-486a-94ae-3996384bebeb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据量较大(1460条)，随机采样1000条进行实验\n"]}, {"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/20 [00:00<?, ?it/s]C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_51936\\**********.py:3: DeprecationWarning: `search` method is deprecated and will be removed in the future. Use `query_points` instead.\n", "  search_response = qdrant_client.search(collection_name='text-classification', query_vector=query_vector, limit=top_k)\n", "100%|██████████| 20/20 [01:32<00:00,  4.61s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["level—1: 1 : text_top=3, label_top=20 -> 准确性: 0.0000, micro_f1: 0.0000, macro_f1: 0.0000\n", "level—2: 1 : text_top=3, label_top=20 -> 准确性: 0.0000, micro_f1: 0.0000, macro_f1: 0.0000\n", "层级约束level—2: 1 : text_top=3, label_top=20 -> 准确性: 0.0000, micro_f1: 0.0000, macro_f1: 0.0000\n", "层级标签: 1 : text_top=3, label_top=20 -> 准确性: 0.0000, micro_f1: 0.0000, macro_f1: 0.0000\n", "层级标签(层级约束): 1 : text_top=3, label_top=20 -> 准确性: 0.0000, micro_f1: 0.0000, macro_f1: 0.0000\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["\n", "def run_evaluation(test_csv, text_top_list, label_top_list, num_iterations=5):\n", "    \"\"\"\n", "    循环评估分类模型性能，支持 text_top 和 label_top 的两层循环。\n", "\n", "    参数：\n", "        test_csv (DataFrame): 包含测试数据的 DataFrame，需要有 'text' 和 'label_text' 列。\n", "        text_top_list (list): text_top 的参数列表。\n", "        label_top_list (list): label_top 的参数列表。\n", "        num_iterations (int): 外层循环的次数。\n", "\n", "    返回：\n", "        None\n", "    \"\"\"\n", "        # 限制实验数据量（可选）\n", "    if len(test_csv) > 1000:\n", "        print(f\"数据量较大({len(test_csv)}条)，随机采样1000条进行实验\")\n", "        test_csv = test_csv.sample(n=20, random_state=42)\n", "\n", "    for text_top in text_top_list:\n", "        for label_top in label_top_list:\n", "            for i in range(num_iterations):\n", "                # 初始化预测列\n", "                test_csv['predicted_label_1'] = None\n", "                test_csv['predicted_label_2'] = None\n", "                test_csv['predicted_label_3'] = None\n", "                # 遍历测试数据集，分类并存储预测结果\n", "                for idx, row in tqdm(test_csv.iterrows(), total=test_csv.shape[0]):\n", "                    Title=row['Title']\n", "                    Text=row['Text']\n", "                    text_title = f\"Tilet: {Title}\\nDescription: {Text}\"\n", "                    query_text=Title\n", "                    try:\n", "                        levels=\"Cat2\"\n", "                        labels=None\n", "                        # 调用分类函数\n", "                        category_l2 = classify_query_label_3(query_text,labels , levels, text_top, label_top)\n", "                        #print(category_l2)\n", "                    except Exception as e:\n", "                        # 捕获分类函数的异常\n", "                        print(f\"Error classifying row {idx}: {e}\")\n", "                        category_l2 = \"Error\"\n", "\n", "                    test_csv.at[idx, 'predicted_label_1'] = category_l2\n", "                \n", "                    try:\n", "                        levels=\"Cat3\"\n", "                        # 调用分类函数\n", "                        category_l3 = classify_query_label_3(query_text, labels, levels, text_top, label_top)\n", "                        #print(category_l3)\n", "                    except Exception as e:\n", "                        # 捕获分类函数的异常\n", "                        print(f\"Error classifying row {idx}: {e}\")\n", "                        category_l3 = \"Error\"\n", "                     \n", "                    test_csv.at[idx, 'predicted_label_2'] = category_l3\n", "                    try:\n", "                            # 验证预测的L3是否属于预测的L2\n", "                            valid_l3_for_l2 = graph_db.query_l3_from_l2(category_l2)\n", "                            if category_l3 not in valid_l3_for_l2:\n", "                                # 重新预测L3，仅使用L2约束的候选\n", "                                if valid_l3_for_l2:\n", "                                    category_l3 = classify_query_label_3(query_text, valid_l3_for_l2, levels, text_top, label_top)\n", "                    except Exception as e:\n", "                        print(f\"层次验证失败: {e}\")\n", "                    test_csv.at[idx, 'predicted_label_3'] = category_l3\n", "                # 从结果中提取真实标签和预测标签\n", "                labels_true_1 = test_csv['Cat2']\n", "                labels_pred_1 = test_csv['predicted_label_1']\n", "                labels_true_2 = test_csv['Cat3']\n", "                labels_pred_2 = test_csv['predicted_label_2']\n", "                labels_pred_3 = test_csv['predicted_label_3']\n", "                # 计算评估指标\n", "                try:\n", "                    accuracy_1 = accuracy_score(labels_true_1, labels_pred_1)\n", "                    micro_f1_1 = f1_score(labels_true_1, labels_pred_1, average='micro')\n", "                    macro_f1_1 = f1_score(labels_true_1, labels_pred_1, average='macro')\n", "\n", "                    print(f\"level—1: {i + 1} : text_top={text_top}, label_top={label_top} -> 准确性: {accuracy_1:.4f}, micro_f1: {micro_f1_1:.4f}, macro_f1: {macro_f1_1:.4f}\")\n", "\n", "\n", "\n", "                    accuracy_2 = accuracy_score(labels_true_2, labels_pred_2)\n", "                    micro_f1_2 = f1_score(labels_true_2, labels_pred_2, average='micro')\n", "                    macro_f1_2 = f1_score(labels_true_2, labels_pred_2, average='macro')\n", "\n", "                    print(f\"level—2: {i + 1} : text_top={text_top}, label_top={label_top} -> 准确性: {accuracy_2:.4f}, micro_f1: {micro_f1_2:.4f}, macro_f1: {macro_f1_2:.4f}\")\n", "\n", "                    accuracy_3 = accuracy_score(labels_true_2, labels_pred_3)\n", "                    micro_f1_3 = f1_score(labels_true_2, labels_pred_3, average='micro')\n", "                    macro_f1_3 = f1_score(labels_true_2, labels_pred_3, average='macro')\n", "\n", "                    print(f\"层级约束level—2: {i + 1} : text_top={text_top}, label_top={label_top} -> 准确性: {accuracy_3:.4f}, micro_f1: {micro_f1_3:.4f}, macro_f1: {macro_f1_3:.4f}\")\n", "                    \n", "                    # L2-L3路径评估（层次化准确率）\n", "                    true_paths = [f\"{l2}->{l3}\" for l2, l3 in zip(labels_true_1, labels_true_2)]\n", "                    pred_paths = [f\"{l2}->{l3}\" for l2, l3 in zip(labels_pred_1, labels_pred_2)]\n", "\n", "                    pred_paths_2 = [f\"{l2}->{l3}\" for l2, l3 in zip(labels_pred_1, labels_pred_3)]\n", "                    \n", "                    path_acc = accuracy_score(true_paths, pred_paths)\n", "                    path_f1_macro = f1_score(true_paths, pred_paths, average='macro', zero_division=0)\n", "                    path_f1_micro = f1_score(true_paths, pred_paths, average='micro', zero_division=0)\n", "                    \n", "                    print(f\"层级标签: {i + 1} : text_top={text_top}, label_top={label_top} -> 准确性: {path_acc:.4f}, micro_f1: {path_f1_micro:.4f}, macro_f1: {path_f1_macro:.4f}\")\n", "\n", "                    path_acc_2 = accuracy_score(true_paths, pred_paths_2)\n", "                    path_f1_macro_2 = f1_score(true_paths, pred_paths_2, average='macro', zero_division=0)\n", "                    path_f1_micro_2 = f1_score(true_paths, pred_paths_2, average='micro', zero_division=0)\n", "                    \n", "                    print(f\"层级标签(层级约束): {i + 1} : text_top={text_top}, label_top={label_top} -> 准确性: {path_acc_2:.4f}, micro_f1: {path_f1_micro_2:.4f}, macro_f1: {path_f1_macro_2:.4f}\")\n", "\n", "                    # test_csv['predicted_label'] = process_labels(test_csv['predicted_label'])\n", "                    # labels_true = test_csv['label_text']\n", "                    # labels_pred = test_csv['predicted_label']\n", "                    # accuracy = accuracy_score(labels_true, labels_pred)\n", "                    # micro_f1 = f1_score(labels_true, labels_pred, average='micro')\n", "                    # macro_f1 = f1_score(labels_true, labels_pred, average='macro')\n", "                    \n", "                    # print(f\"修正{i + 1} : text_top={text_top}, label_top={label_top} -> 准确性: {accuracy:.4f}, micro_f1: {micro_f1:.4f}, macro_f1: {macro_f1:.4f}\")\n", "                    \n", "                except ValueError as e:\n", "                    # 捕获指标计算中的异常\n", "                    print(f\"Error calculating metrics: {e}\")\n", "    \n", "                \n", "             \n", "\n", "# 示例调用\n", "text_top=[3]\n", "label_top=[20]\n", "run_evaluation(test_csv, text_top, label_top, num_iterations=1)\n"]}, {"cell_type": "code", "execution_count": 29, "id": "71c10734", "metadata": {}, "outputs": [], "source": ["from difflib import get_close_matches\n", "import re\n", "\n", "def find_best_match(label, candidates, cutoff=0.9):\n", "    # 先尝试精确匹配\n", "    if label in candidates:\n", "        return label\n", "    # 模糊匹配（返回最相似的1个结果，相似度 > cutoff）\n", "    matches = get_close_matches(label, candidates, n=1, cutoff=cutoff)\n", "    return matches[0] if matches else None\n", "\n", "def clean_label(label, candidate_tags):\n", "    cleaned = re.sub(r'\\n+', ' ', str(label))  # Replace newlines with space\n", "    # 如果是 \"-na-\"，直接保留\n", "    if label == \"-na-\":\n", "        return label\n", "    # 尝试匹配候选标签\n", "    matched = find_best_match(label, candidate_tags)\n", "    if matched:\n", "        return matched\n", "    # 无法匹配时，去除开头的 \"-\" 再试\n", "    \n", "    cleaned = re.sub(r'-+', ' ', cleaned)      # Replace dashes with space\n", "    cleaned = re.sub(r'\\s+', ' ', cleaned)     # Collapse multiple spaces\n", "\n", "    matched = find_best_match(cleaned, candidate_tags)\n", "    if matched:\n", "        return matched\n", "    # 仍然无法匹配，返回清理后的（或标记为未知）\n", "\n", "    return cleaned  # 或 return \"unknown\""]}, {"cell_type": "code", "execution_count": 30, "id": "405949f7-c50a-4d2c-b87f-e25792caded6", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["  0%|          | 0/500 [00:00<?, ?it/s]C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_20908\\**********.py:3: DeprecationWarning: `search` method is deprecated and will be removed in the future. Use `query_points` instead.\n", "  search_response = qdrant_client.search(collection_name='text-classification', query_vector=query_vector, limit=top_k)\n", " 31%|███       | 154/500 [37:18<11:51,  2.06s/it]    "]}, {"name": "stdout", "output_type": "stream", "text": ["发生错误: Connection error.\n", "Error classifying row 998: Unsupported vector type <class 'NoneType'>\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 31%|███       | 155/500 [38:19<1:52:48, 19.62s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["发生错误: Connection error.\n", "层次验证失败: Unsupported vector type <class 'NoneType'>\n", "发生错误: Connection error.\n", "Error classifying row 1450: Unsupported vector type <class 'NoneType'>\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 31%|███       | 156/500 [38:22<1:24:11, 14.68s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["发生错误: Connection error.\n", "Error classifying row 1450: Unsupported vector type <class 'NoneType'>\n", "发生错误: Connection error.\n", "Error classifying row 1163: Unsupported vector type <class 'NoneType'>\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 31%|███▏      | 157/500 [38:25<1:03:59, 11.19s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["发生错误: Connection error.\n", "Error classifying row 1163: Unsupported vector type <class 'NoneType'>\n", "发生错误: Connection error.\n", "Error classifying row 247: Unsupported vector type <class 'NoneType'>\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 32%|███▏      | 158/500 [38:28<49:43,  8.73s/it]  "]}, {"name": "stdout", "output_type": "stream", "text": ["发生错误: Connection error.\n", "Error classifying row 247: Unsupported vector type <class 'NoneType'>\n", "发生错误: Connection error.\n", "Error classifying row 528: Unsupported vector type <class 'NoneType'>\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 32%|███▏      | 159/500 [38:31<39:58,  7.03s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["发生错误: Connection error.\n", "Error classifying row 528: Unsupported vector type <class 'NoneType'>\n", "发生错误: Connection error.\n", "Error classifying row 733: Unsupported vector type <class 'NoneType'>\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 32%|███▏      | 160/500 [38:34<32:51,  5.80s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["发生错误: Connection error.\n", "Error classifying row 733: Unsupported vector type <class 'NoneType'>\n", "发生错误: Connection error.\n", "Error classifying row 32: Unsupported vector type <class 'NoneType'>\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 32%|███▏      | 161/500 [38:37<28:11,  4.99s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["发生错误: Connection error.\n", "Error classifying row 32: Unsupported vector type <class 'NoneType'>\n", "发生错误: Connection error.\n", "Error classifying row 634: Unsupported vector type <class 'NoneType'>\n"]}, {"name": "stderr", "output_type": "stream", "text": [" 32%|███▏      | 162/500 [38:40<24:57,  4.43s/it]"]}, {"name": "stdout", "output_type": "stream", "text": ["发生错误: Connection error.\n", "Error classifying row 634: Unsupported vector type <class 'NoneType'>\n"]}, {"name": "stderr", "output_type": "stream", "text": ["100%|██████████| 500/500 [1:24:27<00:00, 10.13s/it]   \n"]}, {"name": "stdout", "output_type": "stream", "text": ["level—1: text_top=3, label_top=20 -> 准确性: 0.4760, micro_f1: 0.4760, macro_f1: 0.3195\n", "level—2:  text_top=3, label_top=20 -> 准确性: 0.3020, micro_f1: 0.3020, macro_f1: 0.2181\n", "层级约束level—2:text_top=3, label_top=20 -> 准确性: 0.3560, micro_f1: 0.3560, macro_f1: 0.2586\n", "层级标签:  text_top=3, label_top=20 -> 准确性: 0.1960, micro_f1: 0.1960, macro_f1: 0.0668\n", "层级标签(层级约束): text_top=3, label_top=20 -> 准确性: 0.3380, micro_f1: 0.3380, macro_f1: 0.2398\n"]}], "source": ["if len(test_csv) > 1000:\n", "    test_sample = test_csv.sample(n=500, random_state=42)\n", "# 初始化预测列\n", "test_sample['predicted_label_1'] = None\n", "test_sample['predicted_label_2'] = None\n", "test_sample['predicted_label_3'] = None\n", "text_top=3\n", "label_top=20\n", "label_l2=test_csv.Cat2.unique().tolist()\n", "label_l3=test_csv.Cat3.unique().tolist()\n", "# 遍历测试数据集，分类并存储预测结果\n", "for idx, row in tqdm(test_sample.iterrows(), total=test_sample.shape[0]):\n", "    Title=row['Title']\n", "    Text=row['Text']\n", "    text_title = f\"Tilet: {Title}\\nDescription: {Text}\"\n", "    query_text=Title\n", "    try:\n", "        levels=\"Cat2\"\n", "        labels=None\n", "        # 调用分类函数\n", "        category_l2 = classify_query_label_3(query_text,labels , levels, text_top, label_top)\n", "        category_l2=clean_label(category_l2, label_l2)\n", "        #print(category_l2)\n", "    except Exception as e:\n", "        # 捕获分类函数的异常\n", "        print(f\"Error classifying row {idx}: {e}\")\n", "        category_l2 = \"Error\"\n", "\n", "    test_sample.at[idx, 'predicted_label_1'] = category_l2\n", "\n", "    try:\n", "        levels=\"Cat3\"\n", "        # 调用分类函数\n", "        category_l3 = classify_query_label_3(query_text, labels, levels, text_top, label_top)\n", "        category_l3=clean_label(category_l3, label_l3)\n", "        #print(category_l3)\n", "    except Exception as e:\n", "        # 捕获分类函数的异常\n", "        print(f\"Error classifying row {idx}: {e}\")\n", "        category_l3 = \"Error\"\n", "        \n", "    test_sample.at[idx, 'predicted_label_2'] = category_l3\n", "    try:\n", "            # 验证预测的L3是否属于预测的L2\n", "            valid_l3_for_l2 = graph_db.query_l3_from_l2(category_l2)\n", "            if category_l3 not in valid_l3_for_l2:\n", "                # 重新预测L3，仅使用L2约束的候选\n", "                if valid_l3_for_l2:\n", "                    category_l3 = classify_query_label_3(query_text, valid_l3_for_l2, levels, text_top, label_top)\n", "                    category_l3=clean_label(category_l3, label_l3)\n", "    except Exception as e:\n", "        print(f\"层次验证失败: {e}\")\n", "    test_sample.at[idx, 'predicted_label_3'] = category_l3\n", "# 从结果中提取真实标签和预测标签\n", "labels_true_1 = test_sample['Cat2']\n", "labels_pred_1 = test_sample['predicted_label_1']\n", "labels_true_2 = test_sample['Cat3']\n", "labels_pred_2 = test_sample['predicted_label_2']\n", "labels_pred_3 = test_sample['predicted_label_3']\n", "# 计算评估指标\n", "try:\n", "    accuracy_1 = accuracy_score(labels_true_1, labels_pred_1)\n", "    micro_f1_1 = f1_score(labels_true_1, labels_pred_1, average='micro')\n", "    macro_f1_1 = f1_score(labels_true_1, labels_pred_1, average='macro')\n", "\n", "    print(f\"level—1: text_top={text_top}, label_top={label_top} -> 准确性: {accuracy_1:.4f}, micro_f1: {micro_f1_1:.4f}, macro_f1: {macro_f1_1:.4f}\")\n", "\n", "\n", "\n", "    accuracy_2 = accuracy_score(labels_true_2, labels_pred_2)\n", "    micro_f1_2 = f1_score(labels_true_2, labels_pred_2, average='micro')\n", "    macro_f1_2 = f1_score(labels_true_2, labels_pred_2, average='macro')\n", "\n", "    print(f\"level—2:  text_top={text_top}, label_top={label_top} -> 准确性: {accuracy_2:.4f}, micro_f1: {micro_f1_2:.4f}, macro_f1: {macro_f1_2:.4f}\")\n", "\n", "    accuracy_3 = accuracy_score(labels_true_2, labels_pred_3)\n", "    micro_f1_3 = f1_score(labels_true_2, labels_pred_3, average='micro')\n", "    macro_f1_3 = f1_score(labels_true_2, labels_pred_3, average='macro')\n", "\n", "    print(f\"层级约束level—2:text_top={text_top}, label_top={label_top} -> 准确性: {accuracy_3:.4f}, micro_f1: {micro_f1_3:.4f}, macro_f1: {macro_f1_3:.4f}\")\n", "    \n", "    # L2-L3路径评估（层次化准确率）\n", "    true_paths = [f\"{l2}->{l3}\" for l2, l3 in zip(labels_true_1, labels_true_2)]\n", "    pred_paths = [f\"{l2}->{l3}\" for l2, l3 in zip(labels_pred_1, labels_pred_2)]\n", "\n", "    pred_paths_2 = [f\"{l2}->{l3}\" for l2, l3 in zip(labels_pred_1, labels_pred_3)]\n", "    \n", "    path_acc = accuracy_score(true_paths, pred_paths)\n", "    path_f1_macro = f1_score(true_paths, pred_paths, average='macro', zero_division=0)\n", "    path_f1_micro = f1_score(true_paths, pred_paths, average='micro', zero_division=0)\n", "    \n", "    print(f\"层级标签:  text_top={text_top}, label_top={label_top} -> 准确性: {path_acc:.4f}, micro_f1: {path_f1_micro:.4f}, macro_f1: {path_f1_macro:.4f}\")\n", "\n", "    path_acc_2 = accuracy_score(true_paths, pred_paths_2)\n", "    path_f1_macro_2 = f1_score(true_paths, pred_paths_2, average='macro', zero_division=0)\n", "    path_f1_micro_2 = f1_score(true_paths, pred_paths_2, average='micro', zero_division=0)\n", "    \n", "    print(f\"层级标签(层级约束): text_top={text_top}, label_top={label_top} -> 准确性: {path_acc_2:.4f}, micro_f1: {path_f1_micro_2:.4f}, macro_f1: {path_f1_macro_2:.4f}\")\n", "\n", "    # test_csv['predicted_label'] = process_labels(test_csv['predicted_label'])\n", "    # labels_true = test_csv['label_text']\n", "    # labels_pred = test_csv['predicted_label']\n", "    # accuracy = accuracy_score(labels_true, labels_pred)\n", "    # micro_f1 = f1_score(labels_true, labels_pred, average='micro')\n", "    # macro_f1 = f1_score(labels_true, labels_pred, average='macro')\n", "    \n", "    # print(f\"修正{i + 1} : text_top={text_top}, label_top={label_top} -> 准确性: {accuracy:.4f}, micro_f1: {micro_f1:.4f}, macro_f1: {macro_f1:.4f}\")\n", "    \n", "except ValueError as e:\n", "    # 捕获指标计算中的异常\n", "    print(f\"Error calculating metrics: {e}\")"]}, {"cell_type": "code", "execution_count": 24, "id": "62301a59", "metadata": {}, "outputs": [], "source": ["test_sample.to_csv('test_predictions.csv', index=False, encoding='utf-8-sig')"]}, {"cell_type": "code", "execution_count": 28, "id": "1c3b06d9-b192-46aa-8473-b39411c8c6ef", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Title</th>\n", "      <th>Text</th>\n", "      <th>Cat1</th>\n", "      <th>Cat2</th>\n", "      <th>Cat3</th>\n", "      <th>predicted_label_1</th>\n", "      <th>predicted_label_2</th>\n", "      <th>predicted_label_3</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>892</th>\n", "      <td>IceBridge Narrow Swath ATM L1B Qfit Elevation ...</td>\n", "      <td>This data set contains spot elevation measurem...</td>\n", "      <td>earth science</td>\n", "      <td>cryosphere</td>\n", "      <td>sea ice</td>\n", "      <td>cryosphere</td>\n", "      <td>na</td>\n", "      <td>snow/ice</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1105</th>\n", "      <td>Aurora Australis Southern Ocean ADCP data</td>\n", "      <td>Acoustic Doppler current profiler (ADCP) measu...</td>\n", "      <td>earth science</td>\n", "      <td>oceans</td>\n", "      <td>ocean circulation</td>\n", "      <td>oceans</td>\n", "      <td>ocean circulation</td>\n", "      <td>ocean circulation</td>\n", "    </tr>\n", "    <tr>\n", "      <th>413</th>\n", "      <td>Crude Death Rate</td>\n", "      <td>Crude death rate: number  of deaths over a giv...</td>\n", "      <td>earth science</td>\n", "      <td>human dimensions</td>\n", "      <td>population</td>\n", "      <td>human dimensions</td>\n", "      <td>environmental impacts</td>\n", "      <td>environmental impacts</td>\n", "    </tr>\n", "    <tr>\n", "      <th>522</th>\n", "      <td>GCOM-C/SGLI L3 Map Atmospheric corrected refle...</td>\n", "      <td>GCOM-C/SGLI L3 Map Atmospheric corrected refle...</td>\n", "      <td>earth science</td>\n", "      <td>land surface</td>\n", "      <td>surface radiative properties</td>\n", "      <td>atmosphere</td>\n", "      <td>atmospheric/ocean indicators</td>\n", "      <td>atmospheric corrected reflectance</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1036</th>\n", "      <td>Column Liquid Water Data at Ny-Alesund, Svalbird</td>\n", "      <td>Since September 1991, a column of liquid water...</td>\n", "      <td>earth science</td>\n", "      <td>atmosphere</td>\n", "      <td>clouds</td>\n", "      <td>oceans</td>\n", "      <td>ocean winds</td>\n", "      <td>ocean winds</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                  Title  \\\n", "892   IceBridge Narrow Swath ATM L1B Qfit Elevation ...   \n", "1105          Aurora Australis Southern Ocean ADCP data   \n", "413                                    Crude Death Rate   \n", "522   GCOM-C/SGLI L3 Map Atmospheric corrected refle...   \n", "1036   Column Liquid Water Data at Ny-Alesund, Svalbird   \n", "\n", "                                                   Text           Cat1  \\\n", "892   This data set contains spot elevation measurem...  earth science   \n", "1105  Acoustic Doppler current profiler (ADCP) measu...  earth science   \n", "413   Crude death rate: number  of deaths over a giv...  earth science   \n", "522   GCOM-C/SGLI L3 Map Atmospheric corrected refle...  earth science   \n", "1036  Since September 1991, a column of liquid water...  earth science   \n", "\n", "                  Cat2                          Cat3 predicted_label_1  \\\n", "892         cryosphere                       sea ice        cryosphere   \n", "1105            oceans             ocean circulation            oceans   \n", "413   human dimensions                    population  human dimensions   \n", "522       land surface  surface radiative properties        atmosphere   \n", "1036        atmosphere                        clouds            oceans   \n", "\n", "                 predicted_label_2                   predicted_label_3  \n", "892                            na                             snow/ice  \n", "1105             ocean circulation                   ocean circulation  \n", "413          environmental impacts               environmental impacts  \n", "522   atmospheric/ocean indicators   atmospheric corrected reflectance  \n", "1036                   ocean winds                         ocean winds  "]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["test_sample.head()"]}, {"cell_type": "code", "execution_count": 31, "id": "446fc947", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["准确性: 0.8008, micro_f1: 0.8008, macro_f1: 0.7915\n"]}], "source": ["labels_true = test_csv['label_text']\n", "labels_pred = test_csv['predicted_label']\n", "\n", "                # 计算评估指标\n", "try:\n", "    accuracy = accuracy_score(labels_true, labels_pred)\n", "    micro_f1 = f1_score(labels_true, labels_pred, average='micro')\n", "    macro_f1 = f1_score(labels_true, labels_pred, average='macro')\n", "\n", "    print(f\"准确性: {accuracy:.4f}, micro_f1: {micro_f1:.4f}, macro_f1: {macro_f1:.4f}\")\n", "except ValueError as e:\n", "                    # 捕获指标计算中的异常\n", "    print(f\"Error calculating metrics: {e}\")"]}, {"cell_type": "code", "execution_count": 32, "id": "38ae22d2-565a-4ccd-8726-dde590baed8f", "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'datas' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[32], line 24\u001b[0m\n\u001b[1;32m     10\u001b[0m data \u001b[38;5;241m=\u001b[39m {\n\u001b[1;32m     11\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m光学数据产品\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mOptical\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m     12\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m遥感应用数据产品\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mRS Application\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     20\u001b[0m     \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m地形地貌数据\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mTopographic\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m     21\u001b[0m }\n\u001b[1;32m     23\u001b[0m \u001b[38;5;66;03m# 加载聚类结果数据集\u001b[39;00m\n\u001b[0;32m---> 24\u001b[0m clustering_results_df \u001b[38;5;241m=\u001b[39m \u001b[43mdatas\u001b[49m\n\u001b[1;32m     26\u001b[0m \u001b[38;5;66;03m# 假设聚类结果有一个'predicted_label'列，原始数据有一个'Category_Level_2_Numeric'列\u001b[39;00m\n\u001b[1;32m     27\u001b[0m \u001b[38;5;66;03m# 直接从聚类结果数据集中获取这两列数据\u001b[39;00m\n\u001b[1;32m     28\u001b[0m labels_true \u001b[38;5;241m=\u001b[39m clustering_results_df[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mlabel_text\u001b[39m\u001b[38;5;124m'\u001b[39m]\n", "\u001b[0;31mNameError\u001b[0m: name 'datas' is not defined"]}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import confusion_matrix, classification_report, roc_curve, auc\n", "import seaborn as sns\n", "from sklearn.preprocessing import label_binarize\n", "from itertools import cycle\n", "\n", "from sklearn.metrics import accuracy_score\n", "\n", "data = {\n", "    \"光学数据产品\": \"Optical\",\n", "    \"遥感应用数据产品\": \"RS Application\",\n", "    \"遥感反演数据产品\": \"RS Inversion\",\n", "    \"社会经济数据\": \"Socioeconomic\",\n", "    \"基础地理数据\": \"Geographic\",\n", "    \"地面监测数据\": \"Ground\",\n", "    \"遥感解译数据产品\": \"RS Interpretation\",\n", "    \"大气与海洋数据\": \"Atmospheric\",\n", "    \"遥感样本数据\": \"RS Sample\",\n", "    \"地形地貌数据\": \"Topographic\"\n", "}\n", "\n", "# 加载聚类结果数据集\n", "clustering_results_df = datas\n", "\n", "# 假设聚类结果有一个'predicted_label'列，原始数据有一个'Category_Level_2_Numeric'列\n", "# 直接从聚类结果数据集中获取这两列数据\n", "labels_true = clustering_results_df['label_text']\n", "labels_pred = clustering_results_df['predicted_label']\n", "#labels_pred = clustering_results_df['label_llm']\n", "# 计算准确性\n", "accuracy = accuracy_score(labels_true, labels_pred)\n", "print(f'准确性: {accuracy:.4f}')\n", "\n", "\n", "# 生成混淆矩阵\n", "conf_matrix = confusion_matrix(labels_true, labels_pred)\n", "# 将混淆矩阵的行列标签转换为英文\n", "# 将混淆矩阵的行列标签转换为英文\n", "conf_matrix_df = pd.DataFrame(conf_matrix, index=labels_true.unique(), columns=labels_pred.unique())\n", "conf_matrix_df.index = conf_matrix_df.index.map(data)\n", "conf_matrix_df.columns = conf_matrix_df.columns.map(data)\n", "\n", "# 绘制混淆矩阵\n", "plt.figure(figsize=(10, 7))\n", "sns.heatmap(conf_matrix_df, annot=True, fmt='d', cmap='Blues')\n", "plt.xticks(rotation=30)\n", "plt.yticks(rotation=30)\n", "plt.show()\n", "\n", "# 生成分类报告\n", "class_report = classification_report(labels_true, labels_pred)\n", "print(class_report)\n", "\n", "# 添加AUC曲线评估\n", "# 假设标签是多类别的，需要进行二值化处理\n", "labels_true_bin = label_binarize(labels_true, classes=labels_true.unique())\n", "labels_pred_bin = label_binarize(labels_pred, classes=labels_true.unique())\n", "\n", "# 计算每一类的ROC曲线和AUC值\n", "fpr = dict()\n", "tpr = dict()\n", "roc_auc = dict()\n", "n_classes = labels_true_bin.shape[1]\n", "\n", "for i in range(n_classes):\n", "    fpr[i], tpr[i], _ = roc_curve(labels_true_bin[:, i], labels_pred_bin[:, i])\n", "    roc_auc[i] = auc(fpr[i], tpr[i])\n", "\n", "# 绘制所有类别的ROC曲线\n", "plt.figure(figsize=(10, 7))\n", "colors = cycle(['blue', 'red', 'green', 'purple', 'orange', 'brown'])\n", "for i, color in zip(range(n_classes), colors):\n", "    plt.plot(fpr[i], tpr[i], color=color, lw=2,\n", "             label=f'ROC curve of class {i} (area = {roc_auc[i]:.2f})')\n", "\n", "plt.plot([0, 1], [0, 1], 'k--', lw=2)\n", "plt.xlim([0.0, 1.0])\n", "plt.ylim([0.0, 1.05])\n", "plt.xlabel('False Positive Rate')\n", "plt.ylabel('True Positive Rate')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "id": "8d010443-ae02-4a92-9617-d937205bd3f3", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: seaborn in /opt/anaconda3/envs/label/lib/python3.10/site-packages (0.13.2)\n", "Requirement already satisfied: numpy!=1.24.0,>=1.20 in /opt/anaconda3/envs/label/lib/python3.10/site-packages (from seaborn) (2.1.2)\n", "Requirement already satisfied: pandas>=1.2 in /opt/anaconda3/envs/label/lib/python3.10/site-packages (from seaborn) (2.2.3)\n", "Requirement already satisfied: matplotlib!=3.6.1,>=3.4 in /opt/anaconda3/envs/label/lib/python3.10/site-packages (from seaborn) (3.9.2)\n", "Requirement already satisfied: contourpy>=1.0.1 in /opt/anaconda3/envs/label/lib/python3.10/site-packages (from matplotlib!=3.6.1,>=3.4->seaborn) (1.3.1)\n", "Requirement already satisfied: cycler>=0.10 in /opt/anaconda3/envs/label/lib/python3.10/site-packages (from matplotlib!=3.6.1,>=3.4->seaborn) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /opt/anaconda3/envs/label/lib/python3.10/site-packages (from matplotlib!=3.6.1,>=3.4->seaborn) (4.55.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /opt/anaconda3/envs/label/lib/python3.10/site-packages (from matplotlib!=3.6.1,>=3.4->seaborn) (1.4.7)\n", "Requirement already satisfied: packaging>=20.0 in /opt/anaconda3/envs/label/lib/python3.10/site-packages (from matplotlib!=3.6.1,>=3.4->seaborn) (24.2)\n", "Requirement already satisfied: pillow>=8 in /opt/anaconda3/envs/label/lib/python3.10/site-packages (from matplotlib!=3.6.1,>=3.4->seaborn) (11.0.0)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /opt/anaconda3/envs/label/lib/python3.10/site-packages (from matplotlib!=3.6.1,>=3.4->seaborn) (3.2.0)\n", "Requirement already satisfied: python-dateutil>=2.7 in /opt/anaconda3/envs/label/lib/python3.10/site-packages (from matplotlib!=3.6.1,>=3.4->seaborn) (2.9.0)\n", "Requirement already satisfied: pytz>=2020.1 in /opt/anaconda3/envs/label/lib/python3.10/site-packages (from pandas>=1.2->seaborn) (2024.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /opt/anaconda3/envs/label/lib/python3.10/site-packages (from pandas>=1.2->seaborn) (2024.2)\n", "Requirement already satisfied: six>=1.5 in /opt/anaconda3/envs/label/lib/python3.10/site-packages (from python-dateutil>=2.7->matplotlib!=3.6.1,>=3.4->seaborn) (1.16.0)\n", "Note: you may need to restart the kernel to use updated packages.\n"]}], "source": ["pip <PERSON> seaborn"]}], "metadata": {"kernelspec": {"display_name": "py310", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}