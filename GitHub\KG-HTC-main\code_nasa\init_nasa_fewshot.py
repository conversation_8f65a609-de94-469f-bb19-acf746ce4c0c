import pandas as pd
import sys
from pathlib import Path
from tqdm import tqdm
import logging

# 禁用 httpx 的详细日志
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)
# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.path.append(root_dir)

from src.qdrant_vector_db import QdrantVectorDB
from src.graph_db import GraphDB
from src.vector_db import VectorDB


def init_nasa_dataset():
    """
    初始化NASA数据集的知识图谱和向量数据库
    """
    
    # 配置参数
    config = {
        "data_name": "nasa",
        "data_path": "dataset/nasa/nasa_train.csv",
        "output_path": "dataset/nasa/llm_graph_gpt3.json",
        # 注意：QdrantVectorDB使用内存模式，不需要vectdb_path
        "vectdb_path": "database/nasa",
        "template": {
            "sys": "prompts/system/nasa/llm_graph.txt",
            "user": "prompts/user/nasa/llm_graph.txt"
        },
        "query_params": {
            "l2_top_k": 11,  # NASA数据集二级分类较多，增加检索数量
            "l3_top_k": 53   # NASA数据集三级分类很多，增加检索数量
        }
    }

    print("=== 初始化NASA数据集 ===")
    print(f"数据路径: {config['data_path']}")
    
    # 读取预处理后的数据
    try:
        df = pd.read_csv(config["data_path"])
        print(f"成功读取数据: {len(df)} 条记录")
    except FileNotFoundError:
        print(f"错误: 找不到数据文件 {config['data_path']}")
        print("请先运行 preprocess_nasa.py 进行数据预处理")
        return
    
    # 数据质量检查
    print("\n=== 数据质量检查 ===")
    required_columns = ['Title', 'Text', 'Cat1', 'Cat2', 'Cat3']
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        print(f"错误: 缺少必要字段 {missing_columns}")
        return
    
    # 移除空值和unknown标签
    initial_count = len(df)
    df = df.dropna(subset=required_columns)
    df = df[df['Cat1'] != "unknown"]
    df = df[df['Cat2'] != "unknown"] 
    df = df[df['Cat3'] != "unknown"]
    df = df[df['Text'].notna()]
    df = df[df['Title'].notna()]
    
    
    # 初始化数据库连接
    print("\n=== 初始化数据库连接 ===")
    try:
        graph_db = GraphDB()
        print("✓ Neo4j图数据库连接成功")
    except Exception as e:
        print(f"✗ Neo4j图数据库连接失败: {e}")
        print("请确保Neo4j服务正在运行，并检查环境变量配置")
        return
    
    try:
        # QdrantVectorDB构造函数参数：host, port, collection_name, use_memory
        # 使用内存模式，无需database_path参数
        vector_db = VectorDB(
            database_path=config["vectdb_path"],
            collection_name=config["data_name"]
        )
        print("✓ 向量数据库连接成功")
    except Exception as e:
        print(f"✗ 向量数据库连接失败: {e}")
        return
    
    # 构建知识图谱
    print("\n=== 构建知识图谱 ===")
    
    # NASA数据集使用3层层次结构
    query_create_graph = """
    MERGE (level1:Category1 {name: $l1})
    MERGE (level2:Category2 {name: $l2})
    MERGE (level3:Category3 {name: $l3})
    MERGE (level1)-[:contains]->(level2)
    MERGE (level2)-[:contains]->(level3)
    """
    
    # 统计唯一的层次路径
    unique_paths = df[['Cat1', 'Cat2', 'Cat3']].drop_duplicates()
    print(f"唯一层次路径数量: {len(unique_paths)}")
    
    # 批量创建图结构
    print("正在创建图结构...")
    created_count = 0
    error_count = 0
    
    for _, row in tqdm(unique_paths.iterrows(), total=len(unique_paths), desc="创建图节点"):
        try:
            # 标准化标签名称
            l1 = str(row['Cat1']).lower()
            l2 = str(row['Cat2']).lower()
            l3 = str(row['Cat3']).lower()
            
            graph_db.create_database(
                query_create_graph, 
                l1=l1, l2=l2, l3=l3
            )
            created_count += 1
            
        except Exception as e:
            error_count += 1
            if error_count <= 5:  # 只显示前5个错误
                print(f"创建图节点时出错: {e}")
    
    print(f"图结构创建完成: 成功 {created_count} 个，失败 {error_count} 个")
    
    # 构建向量数据库
    print("\n=== 构建向量数据库 ===")
    
    # 提取文本数据（用于向量化）
    texts = []
    metadatas = []
    
    print(f"开始处理 {len(df)} 条记录...")
    
    for idx, row in tqdm(df.iterrows(), total=len(df), desc="准备向量化数据"):
        # 组合标题和文本作为向量化内容
        text_content = f"{row['Title']} {row['Text']}"
        texts.append(text_content)
        
        # 创建元数据（标签作为payload）
        metadata = {
            'Cat1': str(row['Cat1']).lower(),  # 一级分类
            'Cat2': str(row['Cat2']).lower(),  # 二级分类
            'Cat3': str(row['Cat3']).lower(),  # 三级分类
            'Title': str(row['Title']),        # 原始标题
            'Text': str(row["Text"]),
            'level': 'document',               # 标记为文档级别
            'doc_id': str(idx)                 # 文档ID
        }
        metadatas.append(metadata)
    
    print(f"准备完成，共 {len(texts)} 条文本数据")
    
    # 批量添加到向量数据库
    print("正在向量化并添加到数据库...")
    try:
        vector_db.batch_add(
            texts=texts,
            metadatas=metadatas
        )
        print(f"✓ 成功添加 {len(texts)} 条向量化数据")
    except Exception as e:
        print(f"✗ 向量化数据添加失败: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")
        return

if __name__ == "__main__":
    init_nasa_dataset()
