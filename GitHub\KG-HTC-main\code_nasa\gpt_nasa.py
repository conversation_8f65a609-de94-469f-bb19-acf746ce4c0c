import pandas as pd
from pathlib import Path
from tqdm import tqdm
import sys
import json
import time
import logging
from sklearn.metrics import accuracy_score, f1_score, classification_report
import warnings
from urllib3.exceptions import InsecureRequestWarning
# 禁用 httpx 的详细日志
logging.getLogger("httpx").setLevel(logging.WARNING)

# 禁用 SSL 警告
warnings.filterwarnings('ignore', category=InsecureRequestWarning)


# 精确控制各模块的日志级别
logging.getLogger('urllib3').setLevel(logging.ERROR)
logging.getLogger('backoff').setLevel(logging.ERROR)
logging.getLogger('posthog').setLevel(logging.ERROR)
# 完全禁用 backoff 的日志
logging.getLogger('backoff').disabled = True
# Add the root directory to Python path
root_dir = str(Path(__file__).parent.parent)
sys.pah.append(root_dir)

from src.llm import LLM
from src.graph_db import GraphDB
from src.pipeline import Pipeline

def evaluate_full_kg_results(results):
    """
    评估Full-KG实验结果
    """
    print("\n=== Full-KG性能评估 ===")
    
    # 过滤有效预测结果
    valid_results = [r for r in results if r.get('gpt3_graph_l2') != 'error' and r.get('gpt3_graph_l3') != 'error']
    print(f"有效预测记录数: {len(valid_results)}/{len(results)}")
    
    if len(valid_results) == 0:
        print("没有有效的预测结果")
        return {}
    
    # 准备真实标签和预测标签
    true_l2 = [r['Cat2'].lower() for r in valid_results]
    true_l3 = [r['Cat3'].lower() for r in valid_results]
    pred_l2 = [r['gpt3_graph_l2'] for r in valid_results]
    pred_l3 = [r['gpt3_graph_l3'] for r in valid_results]
    
    # L2级别评估
    acc_l2 = accuracy_score(true_l2, pred_l2)
    f1_l2_macro = f1_score(true_l2, pred_l2, average='macro', zero_division=0)
    f1_l2_micro = f1_score(true_l2, pred_l2, average='micro', zero_division=0)
    
    # L3级别评估
    acc_l3 = accuracy_score(true_l3, pred_l3)
    f1_l3_macro = f1_score(true_l3, pred_l3, average='macro', zero_division=0)
    f1_l3_micro = f1_score(true_l3, pred_l3, average='micro', zero_division=0)
    
    # L2-L3路径评估（层次化准确率）
    true_paths = [f"{l2}->{l3}" for l2, l3 in zip(true_l2, true_l3)]
    pred_paths = [f"{l2}->{l3}" for l2, l3 in zip(pred_l2, pred_l3)]
    
    path_acc = accuracy_score(true_paths, pred_paths)
    path_f1_macro = f1_score(true_paths, pred_paths, average='macro', zero_division=0)
    path_f1_micro = f1_score(true_paths, pred_paths, average='micro', zero_division=0)
    
    # 层次化准确率（L2和L3都正确）
    hierarchical_correct = sum(1 for i in range(len(valid_results)) 
                              if true_l2[i] == pred_l2[i] and true_l3[i] == pred_l3[i])
    hierarchical_acc = hierarchical_correct / len(valid_results)
    
    metrics = {
        'accuracy': {
            'L2': acc_l2,
            'L3': acc_l3,
            'L2_L3_Path': path_acc,
            'Hierarchical': hierarchical_acc
        },
        'f1_macro': {
            'L2': f1_l2_macro,
            'L3': f1_l3_macro,
            'L2_L3_Path': path_f1_macro
        },
        'f1_micro': {
            'L2': f1_l2_micro,
            'L3': f1_l3_micro,
            'L2_L3_Path': path_f1_micro
        },
        'statistics': {
            'total_samples': len(results),
            'valid_samples': len(valid_results),
            'success_rate': len(valid_results) / len(results)
        }
    }
    
    # 打印评估结果
    print("\n1. 准确率 (Accuracy):")
    for level, acc in metrics['accuracy'].items():
        print(f"   {level}: {acc:.4f} ({acc*100:.2f}%)")
    
    print("\n2. F1分数 - Macro平均:")
    for level, f1 in metrics['f1_macro'].items():
        print(f"   {level}: {f1:.4f}")
    
    print("\n3. F1分数 - Micro平均:")
    for level, f1 in metrics['f1_micro'].items():
        print(f"   {level}: {f1:.4f}")
    
    return metrics



def run_nasa_experiment():
    """
    运行NASA数据集的KG-HTC分类实验

    该函数实现了基于知识图谱的层次化文本分类实验流程：
    1. 使用QdrantVectorDB进行向量检索，获取相关的L2和L3级别候选标签
    2. 使用GraphDB构建层次化知识图谱，获取标签间的层次关系
    3. 使用LLM进行逐级预测：L1 -> L2 -> L3
    4. 结合向量检索结果和图谱结构，提高分类准确性

    注意：此版本已更新为使用QdrantVectorDB（内存模式），与Pipeline类兼容
    """
    
    # 配置参数 - 已更新为支持QdrantVectorDB
    config = {
        "data_name": "nasa",
        "data_path": "dataset/nasa/nasa_val.csv",
        "output_path": "dataset/nasa/llm_graph_gpt3.json",
        # QdrantVectorDB配置参数
        "vectdb_path": "database/nasa",
        "template": {
            "sys": "prompts/system/nasa/llm_graph.txt",
            "user": "prompts/user/nasa/llm_graph.txt"
        },
        "query_params": {
            "l2_top_k": 6,  # NASA数据集二级分类较多，向量检索L2级别数量
            "l3_top_k": 20   # NASA数据集三级分类很多，向量检索L3级别数量
        }
    }
    
    # 读取数据
    try:
        df = pd.read_csv(config["data_path"])
        print(f"成功读取数据: {len(df)} 条记录")
    except FileNotFoundError:
        print(f"错误: 找不到数据文件 {config['data_path']}")
        print("请先运行 init_nasa.py 进行数据初始化")
        return
    
    # 数据预处理
    df = df.dropna(subset=['Title', 'Text', 'Cat1', 'Cat2', 'Cat3'])
    df = df[df['Cat1'] != "unknown"]
    df = df[df['Cat2'] != "unknown"]
    df = df[df['Cat3'] != "unknown"]
    
    # 限制实验数据量（可选）
    if len(df) > 1000:
        print(f"数据量较大({len(df)}条)，随机采样1000条进行实验")
        df = df.sample(n=500, random_state=42)
    
    ds = df.to_dict(orient="records")
    print(f"实验数据量: {len(ds)} 条记录")
    
    # 初始化模型和数据库
    print("\n=== 初始化模型和数据库 ===")
    try:
        llm = LLM()
        print("✓ LLM初始化成功")
    except Exception as e:
        print(f"✗ LLM初始化失败: {e}")
        print("请检查OpenAI API配置")
        return
    
    try:
        graph_db = GraphDB()
        print("✓ 图数据库连接成功")
    except Exception as e:
        print(f"✗ 图数据库连接失败: {e}")
        return
    
    try:
        # Pipeline会自动初始化QdrantVectorDB，使用config中的参数
        pipeline = Pipeline(llm, config)
        print("✓ Pipeline初始化成功")
              
    except Exception as e:
        print(f"✗ Pipeline初始化失败: {e}")
        return
    
    
    # 开始推理
    print("\n=== 开始分类推理 ===")
    inference_list = []
    success_count = 0
    error_count = 0
    
    for idx in tqdm(range(len(ds)), desc="分类进度"):
        data = ds[idx].copy()
        
        try:
            title = data.get('Title', '').strip()
            text = data.get('Text', '').strip()
            #query_text = f"Title: {title}\n"
            query_txt_vecdb = f"Title: {title}"
            # 构建查询文本 - 结合标题和描述信息
            #query_txt_vecdb = f"Title: {data['Title']}\n"

            # 1. 向量检索相关节点
            # 返回最相似的L2和L3级别标签，用于后续的层次化预测
            retrieved_nodes = pipeline.query_related_nodes(query_txt_vecdb)
            
            # 2. 构建子图
            l3_nodes = retrieved_nodes.get("l3", []) or []
            l2_nodes = retrieved_nodes.get("l2", []) or []

            sub_graph = pipeline.build_linked_labels(l3_nodes, l2_nodes)

            # 3. L2级别预测 - 直接使用所有L2候选和向量检索结果
            potential_level2_candidates = list(set(retrieved_nodes["l2"]))
                
            pred_level2 = pipeline.predict_level_nasa(
                query_txt_vecdb, 
                potential_level2_candidates, 
                sub_graph
            )
            
            # 标准化L2预测结果
            # 4. L3级别预测
            try:
                # 从图数据库中查询L2对应的所有L3子类别
                child_level2 = graph_db.query_l3_from_l2(pred_level2)
                # 结合图谱查询结果和向量检索结果
                potential_level3_candidates = list(set(child_level2 + retrieved_nodes["l3"]))
            except:
                # 如果图查询失败，回退到向量检索结果
                potential_level3_candidates = retrieved_nodes["l3"]
            
            pred_level3 = pipeline.predict_level_nasa_cot(
                query_txt_vecdb, 
                potential_level3_candidates, 
                sub_graph
            )

            # 层次一致性验证
            try:
                # 验证预测的L3是否属于预测的L2
                valid_l3_for_l2 = graph_db.query_l3_from_l2(pred_level2)
                if pred_level3 not in valid_l3_for_l2:
                    print(f"层次不一致: L2={pred_level2}, L3={pred_level3}")
                    # 重新预测L3，仅使用L2约束的候选
                    if valid_l3_for_l2:
                        pred_level3 = pipeline.predict_level_nasa_cot(
                            query_txt_vecdb, 
                            valid_l3_for_l2, 
                            sub_graph
                        )
            except Exception as e:
                print(f"层次验证失败: {e}")
            
            # 标准化L3预测结果
            
            data["gpt3_graph_l2"] = pred_level2
            data["gpt3_graph_l3"] = pred_level3

            # 添加检索和图谱信息用于后续分析
            # 这些信息有助于理解模型的决策过程和调试
            data["retrieved_l2"] = retrieved_nodes["l2"]
            data["retrieved_l3"] = retrieved_nodes["l3"]
            data["subgraph_size"] = len(sub_graph)  # 构建的子图大小，反映知识图谱的贡献
            
            inference_list.append(data)
            success_count += 1
            
        except Exception as e:
            error_count += 1
            print(f"\n处理第 {idx+1} 条记录时出错: {e}")

            data["gpt3_graph_l2"] = "error"
            data["gpt3_graph_l3"] = "error"
            data["error_message"] = str(e)
            inference_list.append(data)

            # 如果错误太多，停止实验避免浪费资源
            if error_count > 50:
                print("错误数量过多，停止实验")
                break
        
        # 添加延迟避免API限制
        time.sleep(0.02)
    
    # 保存最终结果
    print(f"\n=== 保存实验结果 ===")
    try:
        with open(config["output_path"], "w", encoding='utf-8') as f:
            json.dump(inference_list, f, indent=2, ensure_ascii=False)
        print(f"✓ 结果已保存到: {config['output_path']}")
    except Exception as e:
        print(f"✗ 保存结果时出错: {e}")
    
    # 实验统计
    print(f"\n=== 实验统计 ===")
    print(f"总处理记录数: {len(inference_list)}")
    print(f"成功记录数: {success_count}")
    print(f"失败记录数: {error_count}")
    print(f"成功率: {success_count/len(inference_list)*100:.2f}%")
    
    
    evaluate_full_kg_results(inference_list)

if __name__ == "__main__":
    run_nasa_experiment()
    






