from neo4j import GraphDatabase
from neo4j._sync.driver import EagerResult
import os
import dotenv


class GraphDB:
    """
    Neo4j图数据库操作类
    用于管理与Neo4j数据库的连接和查询操作
    """
    
    def __init__(self):
        """
        初始化数据库连接参数
        从环境变量中读取Neo4j数据库的连接信息
        """
        self._URI = "neo4j://localhost:7687"          # 数据库连接URI
        self._USERNAME = "neo4j" # 数据库用户名
        self._PASSWORD = "19980524" # 数据库密码
    
    def create_database(self, query_text: str, **kwargs):
        """
        执行数据库创建/修改操作
        
        Args:
            query_text (str): Cypher查询语句，用于创建节点、关系等
            **kwargs: 查询参数，以键值对形式传递给Cypher语句
        
        功能：
        - 建立数据库连接
        - 执行写入操作（CREATE、MERGE等）
        - 自动关闭连接
        """
        with GraphDatabase.driver(
            self._URI,
            auth=(self._USERNAME, self._PASSWORD),
        ) as driver:
            driver.execute_query(
                query_text,
                **kwargs
            )

    def _query_database(self, query_text: str, **kwargs) -> EagerResult:
        """
        私有方法：执行数据库查询操作
        
        Args:
            query_text (str): Cypher查询语句
            **kwargs: 查询参数
            
        Returns:
            EagerResult: Neo4j查询结果对象，包含查询返回的记录
            
        功能：
        - 建立数据库连接
        - 执行查询操作
        - 返回查询结果
        - 自动关闭连接
        """
        with GraphDatabase.driver(
            self._URI,
            auth=(self._USERNAME, self._PASSWORD),
        ) as driver:
            query_result = driver.execute_query(
                query_text,
                **kwargs
            )
            return query_result
        
    def query_l1_from_l2(self, l2: str) -> str:
        """
        根据二级分类查询对应的一级分类

        Args:
            l2 (str): 二级分类名称

        Returns:
            str: 对应的一级分类名称，如果未找到则返回None

        功能实现：
        - 使用MATCH模式匹配：(level1:Category1)-[:contains]->(level2:Category2)
        - 查找包含指定二级分类的一级分类节点
        - 返回一级分类的name属性值

        图结构：Category1 --contains--> Category2
        """
        query_text = """
        MATCH (level1:Category1)-[:contains]->(level2:Category2 {name: $l2})
        RETURN level1
        """
        try:
            result = self._query_database(query_text, l2=l2)
            if result.records:
                return result.records[0].get("level1").get("name")
            else:
                print(f"警告: 未找到L2分类 '{l2}' 对应的L1分类")
                return None
        except Exception as e:
            print(f"查询L1分类时出错 (L2='{l2}'): {e}")
            return None
    
    def query_l2_from_l3(self, l3: str) -> str:
        """
        根据三级分类查询对应的二级分类

        Args:
            l3 (str): 三级分类名称

        Returns:
            str: 对应的二级分类名称，如果未找到则返回None

        功能实现：
        - 使用MATCH模式匹配：(level2:Category2)-[:contains]->(level3:Category3)
        - 查找包含指定三级分类的二级分类节点
        - 返回二级分类的name属性值

        图结构：Category2 --contains--> Category3
        """
        query_text = """
        MATCH (level2:Category2)-[:contains]->(level3:Category3 {name: $l3})
        RETURN level2
        """
        try:
            result = self._query_database(query_text, l3=l3)
            if result.records:
                return result.records[0].get("level2").get("name")
            else:
                print(f"警告: 未找到L3分类 '{l3}' 对应的L2分类")
                return None
        except Exception as e:
            print(f"查询L2分类时出错 (L3='{l3}'): {e}")
            return None
    
    def query_l2_from_l1(self, l1: str) -> list[str]:
        """
        根据一级分类查询所有对应的二级分类
        
        Args:
            l1 (str): 一级分类名称
            
        Returns:
            list[str]: 该一级分类下所有二级分类名称的列表
            
        功能实现：
        - 使用MATCH模式匹配：(level1:Category1)-[:contains]->(level2:Category2)
        - 查找指定一级分类包含的所有二级分类节点
        - 遍历查询结果，提取所有二级分类的name属性
        - 返回二级分类名称列表
        
        图结构：Category1 --contains--> Category2 (一对多关系)
        """
        query_text = """
        MATCH (level1:Category1 {name: $l1})-[:contains]->(level2:Category2)
        RETURN level2
        """
        try:
            result = self._query_database(query_text, l1=l1)
            return [record.get("level2").get("name") for record in result.records]
        except Exception as e:
            print(f"查询L2分类列表时出错 (L1='{l1}'): {e}")
            return []
    
    def query_l3_from_l2(self, l2: str) -> list[str]:
        """
        根据二级分类查询所有对应的三级分类
        
        Args:
            l2 (str): 二级分类名称
            
        Returns:
            list[str]: 该二级分类下所有三级分类名称的列表
            
        功能实现：
        - 使用MATCH模式匹配：(level2:Category2)-[:contains]->(level3:Category3)
        - 查找指定二级分类包含的所有三级分类节点
        - 遍历查询结果，提取所有三级分类的name属性
        - 返回三级分类名称列表
        
        图结构：Category2 --contains--> Category3 (一对多关系)
        """
        query_text = """
        MATCH (level2:Category2 {name: $l2})-[:contains]->(level3:Category3)
        RETURN level3
        """
        try:
            result = self._query_database(query_text, l2=l2)
            return [record.get("level3").get("name") for record in result.records]
        except Exception as e:
            print(f"查询L3分类列表时出错 (L2='{l2}'): {e}")
            return []
    
