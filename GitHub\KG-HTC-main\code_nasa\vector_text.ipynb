{"cells": [{"cell_type": "code", "execution_count": null, "id": "e22be399", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import sys\n", "from pathlib import Path\n", "from tqdm import tqdm\n", "import logging\n", "\n", "# 禁用 httpx 的详细日志\n", "logging.getLogger(\"httpx\").setLevel(logging.WARNING)\n", "logging.getLogger(\"urllib3\").setLevel(logging.WARNING)\n", "# Add the root directory to Python path\n", "root_dir = str(Path(__file__).parent.parent)\n", "sys.path.append(root_dir)\n", "\n", "from src.qdrant_vector_db import QdrantVectorDB\n", "from src.graph_db import GraphDB\n", "from src.vector_db import VectorDB\n", "\n", "\n", "def init_nasa_dataset():\n", "    \"\"\"\n", "    初始化NASA数据集的知识图谱和向量数据库\n", "    \"\"\"\n", "    \n", "    # 配置参数\n", "    config = {\n", "        \"data_name\": \"nasa-test\",\n", "        \"data_path\": \"dataset/nasa/nasa_train.csv\",\n", "        \"output_path\": \"dataset/nasa/llm_graph_gpt3.json\",\n", "        # 注意：QdrantVectorDB使用内存模式，不需要vectdb_path\n", "        \"vectdb_path\": \"database/nasa-test\",\n", "        \"template\": {\n", "            \"sys\": \"prompts/system/nasa/llm_graph.txt\",\n", "            \"user\": \"prompts/user/nasa/llm_graph.txt\"\n", "        },\n", "        \"query_params\": {\n", "            \"l2_top_k\": 11,  # <PERSON>数据集二级分类较多，增加检索数量\n", "            \"l3_top_k\": 53   # NASA数据集三级分类很多，增加检索数量\n", "        }\n", "    }\n", "\n", "    print(\"=== 初始化NASA数据集 ===\")\n", "    print(f\"数据路径: {config['data_path']}\")\n", "    \n", "    # 读取预处理后的数据\n", "    try:\n", "        df = pd.read_csv(config[\"data_path\"])\n", "        print(f\"成功读取数据: {len(df)} 条记录\")\n", "    except FileNotFoundError:\n", "        print(f\"错误: 找不到数据文件 {config['data_path']}\")\n", "        print(\"请先运行 preprocess_nasa.py 进行数据预处理\")\n", "        return\n", "    \n", "    # 数据质量检查\n", "    print(\"\\n=== 数据质量检查 ===\")\n", "    required_columns = ['Title', 'Text', 'Cat1', 'Cat2', 'Cat3']\n", "    missing_columns = [col for col in required_columns if col not in df.columns]\n", "    if missing_columns:\n", "        print(f\"错误: 缺少必要字段 {missing_columns}\")\n", "        return\n", "    \n", "    # 移除空值和unknown标签\n", "    initial_count = len(df)\n", "    df = df.dropna(subset=required_columns)\n", "    df = df[df['Cat1'] != \"unknown\"]\n", "    df = df[df['Cat2'] != \"unknown\"] \n", "    df = df[df['Cat3'] != \"unknown\"]\n", "    df = df[df['Text'].notna()]\n", "    df = df[df['Title'].notna()]\n", "    \n", "    print(f\"数据清洗后: {len(df)} 条记录 (移除了 {initial_count - len(df)} 条)\")\n", "    \n", "    \n", "    # 初始化数据库连接\n", "    print(\"\\n=== 初始化数据库连接 ===\")\n", "    try:\n", "        # QdrantVectorDB构造函数参数：host, port, collection_name, use_memory\n", "        # 使用内存模式，无需database_path参数\n", "        vector_db = VectorDB(\n", "            database_path=config[\"vectdb_path\"],\n", "            collection_name=config[\"data_name\"]\n", "        )\n", "        print(\"✓ 向量数据库连接成功\")\n", "    except Exception as e:\n", "        print(f\"✗ 向量数据库连接失败: {e}\")\n", "        return\n", "    \n", "    \n", "    # 构建向量数据库\n", "    print(\"\\n=== 构建向量数据库 ===\")\n", "    \n", "    # 准备向量化数据\n", "    print(\"正在准备向量化数据...\")\n", "    \n", "    # 提取文本数据（用于向量化）\n", "    texts = []\n", "    metadatas = []\n", "    \n", "    print(f\"开始处理 {len(df)} 条记录...\")\n", "    \n", "    for idx, row in tqdm(df.iterrows(), total=len(df), desc=\"准备向量化数据\"):\n", "        # 组合标题和文本作为向量化内容\n", "        text_content = f\"{row['Title']} {row['Text']}\"\n", "        texts.append(text_content)\n", "        \n", "        # 创建元数据（标签作为payload）\n", "        metadata = {\n", "            'Cat1': str(row['Cat1']).lower(),  # 一级分类\n", "            'Cat2': str(row['Cat2']).lower(),  # 二级分类\n", "            'Cat3': str(row['Cat3']).lower(),  # 三级分类\n", "            'Title': str(row['Title']),        # 原始标题\n", "            'level': 'document',               # 标记为文档级别\n", "            'doc_id': str(idx)                 # 文档ID\n", "        }\n", "        metadatas.append(metadata)\n", "    \n", "    print(f\"准备完成，共 {len(texts)} 条文本数据\")\n", "    \n", "    # 批量添加到向量数据库\n", "    print(\"正在向量化并添加到数据库...\")\n", "    try:\n", "        vector_db.batch_add(\n", "            texts=texts,\n", "            metadatas=metadatas\n", "        )\n", "        print(f\"✓ 成功添加 {len(texts)} 条向量化数据\")\n", "    except Exception as e:\n", "        print(f\"✗ 向量化数据添加失败: {e}\")\n", "        import traceback\n", "        print(f\"详细错误信息: {traceback.format_exc()}\")\n", "        return\n", "    \n", "    # 验证向量数据库\n", "    print(\"\\n=== 验证向量数据库 ===\")\n", "    try:\n", "        db_info = vector_db.get_collection_info()\n", "        print(f\"向量数据库信息:\")\n", "        print(f\"  - 集合名称: {db_info['name']}\")\n", "        print(f\"  - 文档数量: {db_info['count']}\")\n", "        print(f\"  - 嵌入类型: {db_info['embedding_type']}\")\n", "        \n", "        # 测试查询功能\n", "        print(\"\\n测试查询功能...\")\n", "        test_query = \"space mission\"\n", "        results = vector_db.query_all_levels(test_query, n_results=3)\n", "        \n", "        if results and results.get('documents') and len(results['documents'][0]) > 0:\n", "            print(f\"✓ 查询测试成功，找到 {len(results['documents'][0])} 个相关文档\")\n", "            print(\"前3个相关文档的分类信息:\")\n", "            for i, metadata in enumerate(results['metadatas'][0][:3]):\n", "                print(f\"  {i+1}. Cat1: {metadata.get('Cat1', 'N/A')}, Cat2: {metadata.get('Cat2', 'N/A')}, Cat3: {metadata.get('Cat3', 'N/A')}\")\n", "        else:\n", "            print(\"⚠ 查询测试返回空结果\")\n", "            \n", "    except Exception as e:\n", "        print(f\"✗ 向量数据库验证失败: {e}\")\n", "        import traceback\n", "        print(f\"详细错误信息: {traceback.format_exc()}\")\n", "    \n", "    print(\"\\n=== NASA数据集向量化完成 ===\")\n", "    print(f\"成功向量化 {len(texts)} 条文档\")\n", "    print(\"可以开始使用向量检索功能进行文本分类实验\")\n", "\n", "init_nasa_dataset()\n"]}, {"cell_type": "code", "execution_count": 1, "id": "dbc93239", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from qdrant_client import QdrantClient\n", "#from sentence_transformers import SentenceTransformer\n", "from qdrant_client.http import models\n", "from tqdm import tqdm\n", "\n", "import os\n", "\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import confusion_matrix, classification_report, roc_curve, auc, f1_score\n", "import seaborn as sns\n", "from sklearn.preprocessing import label_binarize\n", "from itertools import cycle\n", "\n", "from sklearn.metrics import accuracy_score\n", "from openai import OpenAI"]}, {"cell_type": "code", "execution_count": 2, "id": "6f7e76a7", "metadata": {}, "outputs": [], "source": ["import httpx\n", "import logging\n", "import warnings\n", "from urllib3.exceptions import InsecureRequestWarning\n", "# 禁用 httpx 的详细日志\n", "logging.getLogger(\"httpx\").setLevel(logging.WARNING)\n", "\n", "# 禁用 SSL 警告\n", "warnings.filterwarnings('ignore', category=InsecureRequestWarning)\n", "\n", "\n", "# 精确控制各模块的日志级别\n", "logging.getLogger('urllib3').setLevel(logging.ERROR)\n", "logging.getLogger('backoff').setLevel(logging.ERROR)\n", "logging.getLogger('posthog').setLevel(logging.ERROR)\n", "# 完全禁用 backoff 的日志\n", "logging.getLogger('backoff').disabled = True"]}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}