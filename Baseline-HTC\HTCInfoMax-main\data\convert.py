import pandas as pd
import os

def convert_excel_to_txt():
    """将Excel文件转换为制表符分隔的文本文件"""
    
    # 输入和输出文件路径
    excel_file = 'WebOfScience/Meta-data/Data.xlsx'
    txt_file = 'WebOfScience/Meta-data/Data.txt'
    
    try:
        # 读取Excel文件
        print("正在读取Excel文件...")
        df = pd.read_excel(excel_file)
        
        # 检查数据格式
        print(f"数据形状: {df.shape}")
        print(f"列数: {len(df.columns)}")
        
        if len(df.columns) != 7:
            print(f"警告：期望7列数据，但发现{len(df.columns)}列")
        
        # 保存为制表符分隔的文本文件
        print("正在保存为文本文件...")
        df.to_csv(txt_file, 
                  sep='\t',           # 制表符分隔
                  index=False,        # 不保存行索引
                  encoding='utf-8',   # UTF-8编码
                  header=True)        # 保留标题行
        
        print(f"转换完成！文件已保存到: {txt_file}")
        print(f"总共处理了 {len(df)} 行数据")
        
        # 验证转换结果
        with open(txt_file, 'r', encoding='utf-8') as f:
            first_line = f.readline().strip()
            print(f"第一行内容: {first_line}")
            
    except FileNotFoundError:
        print(f"错误：找不到文件 {excel_file}")
        print("请确保Excel文件路径正确")
    except Exception as e:
        print(f"转换过程中出现错误: {str(e)}")

if __name__ == '__main__':
    convert_excel_to_txt()